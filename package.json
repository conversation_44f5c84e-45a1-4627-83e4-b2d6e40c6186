{"name": "mobile-device-management", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.0", "@reduxjs/toolkit": "^1.9.5", "axios": "^1.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.0.5", "react-router-dom": "^6.11.1", "socket.io-client": "^4.6.1"}, "devDependencies": {"@types/node": "^18.16.5", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@vitejs/plugin-react": "^4.0.0", "typescript": "^5.0.4", "vite": "^4.3.5"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}}