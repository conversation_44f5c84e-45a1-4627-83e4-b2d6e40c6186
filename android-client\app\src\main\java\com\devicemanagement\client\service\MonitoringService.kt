package com.devicemanagement.client.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.camera2.CameraAccessException
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.CameraDevice
import android.hardware.camera2.CameraManager
import android.hardware.camera2.CaptureRequest
import android.media.Image
import android.media.ImageReader
import android.os.Build
import android.os.Handler
import android.os.HandlerThread
import android.os.IBinder
import android.view.Display
import android.view.Surface
import android.view.WindowManager
import androidx.core.app.NotificationCompat
import com.devicemanagement.client.R
import com.devicemanagement.client.repository.DeviceRepository
import com.devicemanagement.client.ui.MainActivity
import com.devicemanagement.client.util.BatteryUtil
import com.devicemanagement.client.util.LocationUtil
import com.devicemanagement.client.util.PreferenceManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.Semaphore
import java.util.concurrent.TimeUnit
import android.location.Location

/**
 * 前台监控服务，用于截屏和拍照
 */
class MonitoringService : Service() {

    private val deviceRepository by lazy {
        DeviceRepository.getInstance(applicationContext)
    }
    
    private val serviceScope = CoroutineScope(Dispatchers.Default + Job())
    
    private var cameraThread: HandlerThread? = null
    private var cameraHandler: Handler? = null
    private var cameraDevice: CameraDevice? = null
    private val cameraOpenCloseLock = Semaphore(1)
    
    private var isScreenMonitoringEnabled = false
    private var isCameraMonitoringEnabled = false
    private var monitoringJob: Job? = null

    override fun onCreate() {
        super.onCreate()
        Timber.d("监控服务创建")
        
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())
        
        // 初始化相机线程
        cameraThread = HandlerThread("CameraThread").apply { start() }
        cameraHandler = Handler(cameraThread!!.looper)
        
        isRunning = true
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Timber.d("监控服务启动")
        
        // 读取配置
        loadConfiguration()
        
        // 启动监控任务
        startMonitoring()
        
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        Timber.d("监控服务销毁")
        
        // 停止监控任务
        stopMonitoring()
        
        // 释放相机资源
        closeCamera()
        
        cameraThread?.quitSafely()
        
        isRunning = false
        
        super.onDestroy()
    }

    /**
     * 加载配置
     */
    private fun loadConfiguration() {
        isScreenMonitoringEnabled = PreferenceManager.getBoolean(
            applicationContext,
            PreferenceManager.KEY_SCREEN_MONITOR_ENABLED,
            false
        )
        
        isCameraMonitoringEnabled = PreferenceManager.getBoolean(
            applicationContext,
            PreferenceManager.KEY_CAMERA_MONITOR_ENABLED,
            false
        )
        
        Timber.d("配置加载完成：屏幕监控=$isScreenMonitoringEnabled，相机监控=$isCameraMonitoringEnabled")
    }

    /**
     * 启动监控任务
     */
    private fun startMonitoring() {
        stopMonitoring()
        
        monitoringJob = serviceScope.launch {
            var lastLocationUploadTime = 0L
            while (true) {
                try {
                    // 检查低电量，低电量时暂停监控
                    val lowBatteryThreshold = PreferenceManager.getInt(
                        applicationContext,
                        PreferenceManager.KEY_LOW_BATTERY_THRESHOLD,
                        20
                    )
                    if (BatteryUtil.isLowBattery(applicationContext, lowBatteryThreshold)) {
                        Timber.w("电池电量过低，暂停监控任务以节省电量")
                        delay(5 * 60 * 1000L)
                        continue
                    }

                    // 定时上传地理位置
                    val locationTrackingEnabled = PreferenceManager.getBoolean(
                        applicationContext,
                        PreferenceManager.KEY_LOCATION_TRACKING_ENABLED,
                        false
                    )
                    val locationUploadInterval = PreferenceManager.getInt(
                        applicationContext,
                        PreferenceManager.KEY_LOCATION_UPLOAD_INTERVAL,
                        300
                    ) * 1000L
                    val now = System.currentTimeMillis()
                    if (locationTrackingEnabled && now - lastLocationUploadTime >= locationUploadInterval) {
                        if (LocationUtil.hasLocationPermission(applicationContext)) {
                            val deviceId = PreferenceManager.getString(
                                applicationContext,
                                PreferenceManager.KEY_DEVICE_ID,
                                ""
                            ) ?: ""
                            val deviceToken = PreferenceManager.getString(
                                applicationContext,
                                PreferenceManager.KEY_DEVICE_TOKEN,
                                ""
                            ) ?: ""
                            if (deviceId.isNotEmpty() && deviceToken.isNotEmpty()) {
                                val location: Location? = LocationUtil.getCurrentLocation(applicationContext)
                                if (location != null) {
                                    deviceRepository.uploadLocation(
                                        deviceId,
                                        deviceToken,
                                        location.latitude,
                                        location.longitude,
                                        now
                                    )
                                    Timber.d("地理位置已上传：${location.latitude},${location.longitude}")
                                } else {
                                    Timber.w("未能获取到当前位置")
                                }
                            }
                        } else {
                            Timber.w("无定位权限，无法上传地理位置")
                        }
                        lastLocationUploadTime = now
                    }
                    
                    // 检查设备是否连接
                    val isConnected = PreferenceManager.getBoolean(
                        applicationContext,
                        PreferenceManager.KEY_IS_CONNECTED,
                        false
                    )
                    
                    if (!isConnected) {
                        Timber.d("设备未连接，跳过监控")
                        delay(CHECK_CONNECTION_INTERVAL)
                        continue
                    }
                    
                    // 屏幕监控
                    if (isScreenMonitoringEnabled) {
                        captureScreen()
                    }
                    
                    // 相机监控
                    if (isCameraMonitoringEnabled) {
                        captureCamera()
                    }
                    
                    // 间隔
                    delay(MONITORING_INTERVAL)
                } catch (e: Exception) {
                    Timber.e(e, "监控任务异常")
                    delay(ERROR_RETRY_INTERVAL)
                }
            }
        }
    }

    /**
     * 停止监控任务
     */
    private fun stopMonitoring() {
        monitoringJob?.cancel()
        monitoringJob = null
    }

    /**
     * 截取屏幕
     */
    private suspend fun captureScreen() {
        try {
            // 获取设备ID和Token
            val deviceId = PreferenceManager.getString(
                applicationContext,
                PreferenceManager.KEY_DEVICE_ID,
                ""
            ) ?: return
            
            val deviceToken = PreferenceManager.getString(
                applicationContext,
                PreferenceManager.KEY_DEVICE_TOKEN,
                ""
            ) ?: return
            
            // 创建临时文件
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "screen_$timestamp.jpg"
            val file = File(getExternalFilesDir(null), fileName)
            
            // 截屏
            val display = (getSystemService(Context.WINDOW_SERVICE) as WindowManager).defaultDisplay
            val width = display.width
            val height = display.height
            
            // 注意：实际实现需要适当的权限和系统API支持
            // 这里使用模拟数据作为示例
            val bitmap = createDummyScreenshot(width, height)
            
            // 保存到文件
            FileOutputStream(file).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
            }
            
            // 上传到服务器
            deviceRepository.uploadScreenshot(deviceId, deviceToken, file.absolutePath)
            
            // 删除临时文件
            file.delete()
            
            Timber.d("屏幕截图已上传")
        } catch (e: Exception) {
            Timber.e(e, "截屏失败")
        }
    }

    /**
     * 模拟截屏（实际应用中需要实现真实的截屏功能）
     */
    private fun createDummyScreenshot(width: Int, height: Int): Bitmap {
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = android.graphics.Canvas(bitmap)
        canvas.drawColor(android.graphics.Color.GRAY)
        
        val paint = android.graphics.Paint().apply {
            color = android.graphics.Color.WHITE
            textSize = 50f
            textAlign = android.graphics.Paint.Align.CENTER
        }
        
        val text = "设备管理客户端"
        canvas.drawText(text, width / 2f, height / 2f, paint)
        
        val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
        canvas.drawText(timestamp, width / 2f, height / 2f + 100, paint)
        
        return bitmap
    }

    /**
     * 拍摄相机照片
     */
    private suspend fun captureCamera() {
        try {
            // 获取设备ID和Token
            val deviceId = PreferenceManager.getString(
                applicationContext,
                PreferenceManager.KEY_DEVICE_ID,
                ""
            ) ?: return
            
            val deviceToken = PreferenceManager.getString(
                applicationContext,
                PreferenceManager.KEY_DEVICE_TOKEN,
                ""
            ) ?: return
            
            // 创建临时文件
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "camera_$timestamp.jpg"
            val file = File(getExternalFilesDir(null), fileName)
            
            // 使用Camera2 API拍照
            val imageBytes = takePicture()
            
            if (imageBytes != null) {
                // 保存到文件
                FileOutputStream(file).use { out ->
                    out.write(imageBytes)
                }
                
                // 上传到服务器
                deviceRepository.uploadCameraImage(deviceId, deviceToken, file.absolutePath)
                
                // 删除临时文件
                file.delete()
                
                Timber.d("相机照片已上传")
            }
        } catch (e: Exception) {
            Timber.e(e, "拍照失败")
        }
    }

    /**
     * 使用Camera2 API拍照
     */
    private suspend fun takePicture(): ByteArray? {
        return try {
            val imageBytes = kotlin.coroutines.suspendCoroutine { continuation ->
                val cameraManager = getSystemService(Context.CAMERA_SERVICE) as CameraManager
                
                try {
                    // 获取后置摄像头ID
                    val cameraId = cameraManager.cameraIdList.firstOrNull { id ->
                        val characteristics = cameraManager.getCameraCharacteristics(id)
                        val facing = characteristics.get(CameraCharacteristics.LENS_FACING)
                        facing == CameraCharacteristics.LENS_FACING_BACK
                    } ?: cameraManager.cameraIdList.firstOrNull()
                    
                    if (cameraId == null) {
                        continuation.resumeWith(Result.failure(Exception("没有可用的摄像头")))
                        return@suspendCoroutine
                    }
                    
                    // 打开摄像头
                    if (!cameraOpenCloseLock.tryAcquire(2500, TimeUnit.MILLISECONDS)) {
                        throw Exception("无法获取相机锁")
                    }
                    
                    // 获取相机分辨率
                    val characteristics = cameraManager.getCameraCharacteristics(cameraId)
                    val streamConfigurationMap = characteristics.get(
                        CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP
                    )
                    
                    // 获取合适的图片大小
                    val quality = PreferenceManager.getInt(
                        applicationContext,
                        PreferenceManager.KEY_CAMERA_QUALITY,
                        1
                    )
                    
                    val size = when (quality) {
                        0 -> streamConfigurationMap!!.getOutputSizes(ImageReader::class.java).minByOrNull { it.width * it.height }
                        2 -> streamConfigurationMap!!.getOutputSizes(ImageReader::class.java).maxByOrNull { it.width * it.height }
                        else -> streamConfigurationMap!!.getOutputSizes(ImageReader::class.java).sortedBy { it.width * it.height }[streamConfigurationMap.getOutputSizes(ImageReader::class.java).size / 2]
                    }
                    
                    if (size == null) {
                        continuation.resumeWith(Result.failure(Exception("无法获取相机分辨率")))
                        return@suspendCoroutine
                    }
                    
                    // 创建图像读取器
                    val imageReader = ImageReader.newInstance(size.width, size.height, PixelFormat.RGBA_8888, 1)
                    
                    // 图像可用时的回调
                    val onImageAvailableListener = ImageReader.OnImageAvailableListener { reader ->
                        try {
                            val image = reader.acquireLatestImage()
                            
                            if (image != null) {
                                val bytes = imageToByteArray(image)
                                image.close()
                                reader.close()
                                
                                closeCamera()
                                cameraOpenCloseLock.release()
                                
                                continuation.resumeWith(Result.success(bytes))
                            } else {
                                closeCamera()
                                cameraOpenCloseLock.release()
                                continuation.resumeWith(Result.failure(Exception("无法获取图像")))
                            }
                        } catch (e: Exception) {
                            closeCamera()
                            cameraOpenCloseLock.release()
                            continuation.resumeWith(Result.failure(e))
                        }
                    }
                    
                    imageReader.setOnImageAvailableListener(onImageAvailableListener, cameraHandler)
                    
                    cameraManager.openCamera(cameraId, object : CameraDevice.StateCallback() {
                        override fun onOpened(camera: CameraDevice) {
                            cameraDevice = camera
                            
                            try {
                                // 创建拍照会话
                                val captureRequestBuilder = camera.createCaptureRequest(CameraDevice.TEMPLATE_STILL_CAPTURE)
                                captureRequestBuilder.addTarget(imageReader.surface)
                                
                                // 自动对焦
                                captureRequestBuilder.set(CaptureRequest.CONTROL_AF_MODE, CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE)
                                
                                // 自动曝光
                                captureRequestBuilder.set(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON_AUTO_FLASH)
                                
                                // 创建拍照会话
                                camera.createCaptureSession(
                                    listOf(imageReader.surface),
                                    object : CameraCaptureSession.StateCallback() {
                                        override fun onConfigured(session: CameraCaptureSession) {
                                            try {
                                                // 拍照
                                                session.capture(
                                                    captureRequestBuilder.build(),
                                                    null,
                                                    cameraHandler
                                                )
                                            } catch (e: CameraAccessException) {
                                                closeCamera()
                                                cameraOpenCloseLock.release()
                                                continuation.resumeWith(Result.failure(e))
                                            }
                                        }
                                        
                                        override fun onConfigureFailed(session: CameraCaptureSession) {
                                            closeCamera()
                                            cameraOpenCloseLock.release()
                                            continuation.resumeWith(Result.failure(Exception("相机会话配置失败")))
                                        }
                                    },
                                    cameraHandler
                                )
                            } catch (e: CameraAccessException) {
                                closeCamera()
                                cameraOpenCloseLock.release()
                                continuation.resumeWith(Result.failure(e))
                            }
                        }
                        
                        override fun onDisconnected(camera: CameraDevice) {
                            cameraOpenCloseLock.release()
                            camera.close()
                            cameraDevice = null
                            continuation.resumeWith(Result.failure(Exception("相机断开连接")))
                        }
                        
                        override fun onError(camera: CameraDevice, error: Int) {
                            cameraOpenCloseLock.release()
                            camera.close()
                            cameraDevice = null
                            continuation.resumeWith(Result.failure(Exception("相机错误: $error")))
                        }
                    }, cameraHandler)
                } catch (e: CameraAccessException) {
                    cameraOpenCloseLock.release()
                    continuation.resumeWith(Result.failure(e))
                } catch (e: InterruptedException) {
                    cameraOpenCloseLock.release()
                    continuation.resumeWith(Result.failure(e))
                } catch (e: Exception) {
                    cameraOpenCloseLock.release()
                    continuation.resumeWith(Result.failure(e))
                }
            }
            
            imageBytes
        } catch (e: Exception) {
            Timber.e(e, "拍照失败")
            null
        }
    }

    /**
     * 将Image转换为ByteArray
     */
    private fun imageToByteArray(image: Image): ByteArray {
        val planes = image.planes
        val buffer = planes[0].buffer
        val pixelStride = planes[0].pixelStride
        val rowStride = planes[0].rowStride
        val rowPadding = rowStride - pixelStride * image.width
        
        // 创建位图
        val bitmap = Bitmap.createBitmap(
            image.width + rowPadding / pixelStride,
            image.height,
            Bitmap.Config.ARGB_8888
        )
        bitmap.copyPixelsFromBuffer(buffer)
        
        // 转换为JPEG
        val baos = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 90, baos)
        return baos.toByteArray()
    }

    /**
     * 关闭相机
     */
    private fun closeCamera() {
        try {
            cameraOpenCloseLock.acquire()
            cameraDevice?.close()
            cameraDevice = null
        } catch (e: InterruptedException) {
            Timber.e(e, "关闭相机时中断")
        } finally {
            cameraOpenCloseLock.release()
        }
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "设备管理监控服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "用于保持设备管理监控服务在后台运行"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 创建前台服务通知
     */
    private fun createNotification(): Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("设备管理客户端")
            .setContentText("监控服务正在运行")
            .setSmallIcon(R.drawable.ic_notification)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }

    /**
     * 执行规则指定的监控动作
     */
    private suspend fun executeRuleAction(action: String) {
        when (action) {
            "CAPTURE_SCREEN" -> captureScreen()
            "CAPTURE_CAMERA" -> captureCamera()
            // 可扩展更多动作，如录音、上传文件等
        }
    }

    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "device_management_service_channel"
        
        private const val MONITORING_INTERVAL = 60000L // 1分钟
        private const val CHECK_CONNECTION_INTERVAL = 30000L // 30秒
        private const val ERROR_RETRY_INTERVAL = 10000L // 10秒
        
        var isRunning = false
            private set
    }
    
    /**
     * Camera2 API的CameraCaptureSession回调
     */
    private abstract class CameraCaptureSession {
        abstract class StateCallback {
            abstract fun onConfigured(session: Any)
            abstract fun onConfigureFailed(session: Any)
        }
    }
} 