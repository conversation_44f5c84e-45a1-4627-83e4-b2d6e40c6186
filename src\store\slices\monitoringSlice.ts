import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface MonitoringState {
  screen: {
    isEnabled: boolean;
    isRecording: boolean;
    quality: 'low' | 'medium' | 'high';
    refreshRate: number; // 秒
  };
  audio: {
    isEnabled: boolean;
    isRecording: boolean;
    quality: 'low' | 'medium' | 'high';
  };
  location: {
    isEnabled: boolean;
    isTracking: boolean;
    interval: number; // 秒
    history: {
      latitude: number;
      longitude: number;
      timestamp: string;
    }[];
  };
  camera: {
    isEnabled: boolean;
    isRecording: boolean;
    currentCamera: 'front' | 'back';
  };
}

const initialState: MonitoringState = {
  screen: {
    isEnabled: false,
    isRecording: false,
    quality: 'medium',
    refreshRate: 1,
  },
  audio: {
    isEnabled: false,
    isRecording: false,
    quality: 'medium',
  },
  location: {
    isEnabled: false,
    isTracking: false,
    interval: 10,
    history: [],
  },
  camera: {
    isEnabled: false,
    isRecording: false,
    currentCamera: 'back',
  },
};

export const monitoringSlice = createSlice({
  name: 'monitoring',
  initialState,
  reducers: {
    // 屏幕监控
    toggleScreenMonitoring: (state, action: PayloadAction<boolean>) => {
      state.screen.isEnabled = action.payload;
      if (!action.payload) {
        state.screen.isRecording = false;
      }
    },
    toggleScreenRecording: (state, action: PayloadAction<boolean>) => {
      state.screen.isRecording = action.payload;
    },
    setScreenQuality: (
      state,
      action: PayloadAction<MonitoringState['screen']['quality']>
    ) => {
      state.screen.quality = action.payload;
    },
    setScreenRefreshRate: (state, action: PayloadAction<number>) => {
      state.screen.refreshRate = action.payload;
    },

    // 音频监控
    toggleAudioMonitoring: (state, action: PayloadAction<boolean>) => {
      state.audio.isEnabled = action.payload;
      if (!action.payload) {
        state.audio.isRecording = false;
      }
    },
    toggleAudioRecording: (state, action: PayloadAction<boolean>) => {
      state.audio.isRecording = action.payload;
    },
    setAudioQuality: (
      state,
      action: PayloadAction<MonitoringState['audio']['quality']>
    ) => {
      state.audio.quality = action.payload;
    },

    // 位置监控
    toggleLocationTracking: (state, action: PayloadAction<boolean>) => {
      state.location.isEnabled = action.payload;
      if (!action.payload) {
        state.location.isTracking = false;
      }
    },
    startLocationTracking: (state, action: PayloadAction<boolean>) => {
      state.location.isTracking = action.payload;
    },
    setLocationInterval: (state, action: PayloadAction<number>) => {
      state.location.interval = action.payload;
    },
    addLocationHistory: (
      state,
      action: PayloadAction<{
        latitude: number;
        longitude: number;
        timestamp: string;
      }>
    ) => {
      state.location.history.push(action.payload);
    },
    clearLocationHistory: (state) => {
      state.location.history = [];
    },

    // 摄像头监控
    toggleCameraMonitoring: (state, action: PayloadAction<boolean>) => {
      state.camera.isEnabled = action.payload;
      if (!action.payload) {
        state.camera.isRecording = false;
      }
    },
    toggleCameraRecording: (state, action: PayloadAction<boolean>) => {
      state.camera.isRecording = action.payload;
    },
    switchCamera: (
      state,
      action: PayloadAction<MonitoringState['camera']['currentCamera']>
    ) => {
      state.camera.currentCamera = action.payload;
    },

    // 重置所有监控
    resetAllMonitoring: () => initialState,
  },
});

export const {
  toggleScreenMonitoring,
  toggleScreenRecording,
  setScreenQuality,
  setScreenRefreshRate,
  toggleAudioMonitoring,
  toggleAudioRecording,
  setAudioQuality,
  toggleLocationTracking,
  startLocationTracking,
  setLocationInterval,
  addLocationHistory,
  clearLocationHistory,
  toggleCameraMonitoring,
  toggleCameraRecording,
  switchCamera,
  resetAllMonitoring,
} = monitoringSlice.actions;

export default monitoringSlice.reducer; 