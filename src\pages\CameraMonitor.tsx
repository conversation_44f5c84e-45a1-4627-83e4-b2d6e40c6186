import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  Divider,
  Switch,
  FormControlLabel,
  FormGroup,
  Slider,
  Select,
  MenuItem,
  FormControl,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  FlipCameraAndroid as FlipCameraIcon,
  Screenshot as ScreenshotIcon,
  Download as DownloadIcon,
  Videocam as VideocamIcon,
  PhotoCamera as PhotoCameraIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { RootState } from '@store/index';
import { 
  toggleCameraMonitoring, 
  toggleCameraRecording, 
  switchCamera 
} from '@store/slices/monitoringSlice';

const CameraMonitor: React.FC = () => {
  const dispatch = useDispatch();
  const { isConnected } = useSelector((state: RootState) => state.device);
  const { camera } = useSelector((state: RootState) => state.monitoring);
  
  const [quality, setQuality] = useState<string>('medium');
  const [zoomLevel, setZoomLevel] = useState<number>(1);

  // 处理开关摄像头监控
  const handleToggleMonitor = () => {
    dispatch(toggleCameraMonitoring(!camera.isEnabled));
  };

  // 处理开始/停止录制
  const handleToggleRecording = () => {
    dispatch(toggleCameraRecording(!camera.isRecording));
  };

  // 切换前后摄像头
  const handleSwitchCamera = () => {
    dispatch(switchCamera(camera.currentCamera === 'front' ? 'back' : 'front'));
  };

  // 处理截图
  const handleTakeScreenshot = () => {
    console.log('Taking screenshot');
    // 实际项目中这里应该调用API来获取截图
  };

  // 处理质量设置变更
  const handleQualityChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setQuality(event.target.value as string);
  };

  // 处理缩放级别变更
  const handleZoomChange = (_event: Event, newValue: number | number[]) => {
    setZoomLevel(newValue as number);
  };

  if (!isConnected) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h5" color="error" gutterBottom>
          未连接设备
        </Typography>
        <Typography variant="body1">
          请先在仪表盘页面连接一个设备以使用摄像监控功能。
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h5" component="h1">
              摄像监控
            </Typography>
            <FormGroup>
              <FormControlLabel
                control={
                  <Switch
                    checked={camera.isEnabled}
                    onChange={handleToggleMonitor}
                    color="primary"
                  />
                }
                label={camera.isEnabled ? "监控已开启" : "监控已关闭"}
              />
            </FormGroup>
          </Box>
        </Grid>

        <Grid item xs={12} md={8}>
          <Card sx={{ height: '100%', minHeight: 400 }}>
            {camera.isEnabled ? (
              <Box
                sx={{
                  height: '100%',
                  minHeight: 400,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'black',
                  position: 'relative',
                }}
              >
                {/* 在实际项目中，这里应该是一个视频流 */}
                <Typography variant="h6" color="white">
                  {camera.currentCamera === 'front' ? '前置' : '后置'}摄像头实时画面
                </Typography>
                
                {/* 录制指示器 */}
                {camera.isRecording && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 16,
                      left: 16,
                      display: 'flex',
                      alignItems: 'center',
                      color: 'error.main',
                    }}
                  >
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        bgcolor: 'error.main',
                        mr: 1,
                        animation: 'pulse 1.5s infinite ease-in-out',
                        '@keyframes pulse': {
                          '0%': { opacity: 1 },
                          '50%': { opacity: 0.5 },
                          '100%': { opacity: 1 },
                        },
                      }}
                    />
                    <Typography variant="body2">录制中</Typography>
                  </Box>
                )}
                
                {/* 底部控制栏 */}
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    p: 2,
                    bgcolor: 'rgba(0,0,0,0.5)',
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                >
                  <IconButton color="primary" onClick={handleToggleRecording}>
                    {camera.isRecording ? <StopIcon /> : <PlayIcon />}
                  </IconButton>
                  <IconButton color="primary" onClick={handleSwitchCamera}>
                    <FlipCameraIcon />
                  </IconButton>
                  <IconButton color="primary" onClick={handleTakeScreenshot}>
                    <ScreenshotIcon />
                  </IconButton>
                </Box>
              </Box>
            ) : (
              <Box
                sx={{
                  height: '100%',
                  minHeight: 400,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: '#f5f5f5',
                }}
              >
                <VideocamIcon sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  摄像监控未开启
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<PlayIcon />}
                  onClick={handleToggleMonitor}
                >
                  开启监控
                </Button>
              </Box>
            )}
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                监控设置
              </Typography>
              <Divider sx={{ my: 2 }} />
              
              <Box sx={{ mb: 3 }}>
                <Typography id="camera-quality-label" gutterBottom>
                  画面质量
                </Typography>
                <FormControl fullWidth size="small">
                  <Select
                    value={quality}
                    onChange={handleQualityChange}
                    disabled={!camera.isEnabled}
                  >
                    <MenuItem value="low">低 (流畅)</MenuItem>
                    <MenuItem value="medium">中 (标准)</MenuItem>
                    <MenuItem value="high">高 (清晰)</MenuItem>
                  </Select>
                </FormControl>
              </Box>
              
              <Box sx={{ mb: 3 }}>
                <Typography id="zoom-level-slider" gutterBottom>
                  缩放级别: {zoomLevel}x
                </Typography>
                <Slider
                  value={zoomLevel}
                  onChange={handleZoomChange}
                  aria-labelledby="zoom-level-slider"
                  step={0.5}
                  marks
                  min={1}
                  max={5}
                  disabled={!camera.isEnabled}
                />
              </Box>
              
              <Box sx={{ mb: 3 }}>
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={camera.isRecording}
                        onChange={handleToggleRecording}
                        disabled={!camera.isEnabled}
                      />
                    }
                    label="录制视频"
                  />
                </FormGroup>
              </Box>

              <Divider sx={{ my: 2 }} />
              
              <Box sx={{ mt: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<DownloadIcon />}
                  fullWidth
                  sx={{ mb: 1 }}
                  disabled={!camera.isEnabled}
                >
                  下载录像
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<PhotoCameraIcon />}
                  fullWidth
                  sx={{ mb: 1 }}
                  disabled={!camera.isEnabled}
                  onClick={handleTakeScreenshot}
                >
                  拍摄照片
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<SettingsIcon />}
                  fullWidth
                  disabled={!camera.isEnabled}
                >
                  高级设置
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CameraMonitor; 