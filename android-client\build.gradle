// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        kotlin_version = '1.7.10'
        core_ktx_version = '1.9.0'
        appcompat_version = '1.6.1'
        material_version = '1.8.0'
        constraint_layout_version = '2.1.4'
        lifecycle_version = '2.6.1'
        navigation_version = '2.5.3'
        room_version = '2.5.1'
        retrofit_version = '2.9.0'
        okhttp_version = '4.10.0'
        gson_version = '2.10.1'
        coroutines_version = '1.6.4'
        socket_io_version = '2.1.0'
        work_manager_version = '2.8.1'
        camerax_version = '1.2.2'
    }

    repositories {
        google()
        mavenCentral()
    }
    
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "androidx.navigation:navigation-safe-args-gradle-plugin:$navigation_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
} 