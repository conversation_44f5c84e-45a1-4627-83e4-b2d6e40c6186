import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface DeviceState {
  id: string;
  token: string;
  isConnected: boolean;
  systemInfo: {
    version: string;
    model: string;
    battery: number;
    storage: {
      total: number;
      used: number;
    };
  } | null;
  lastConnected: string | null;
}

const initialState: DeviceState = {
  id: '',
  token: '',
  isConnected: false,
  systemInfo: null,
  lastConnected: null,
};

export const deviceSlice = createSlice({
  name: 'device',
  initialState,
  reducers: {
    setDeviceCredentials: (
      state,
      action: PayloadAction<{ id: string; token: string }>
    ) => {
      state.id = action.payload.id;
      state.token = action.payload.token;
    },
    setConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
      if (action.payload) {
        state.lastConnected = new Date().toISOString();
      }
    },
    setSystemInfo: (
      state,
      action: PayloadAction<DeviceState['systemInfo']>
    ) => {
      state.systemInfo = action.payload;
    },
    disconnectDevice: (state) => {
      state.isConnected = false;
    },
    resetDevice: () => initialState,
  },
});

export const {
  setDeviceCredentials,
  setConnectionStatus,
  setSystemInfo,
  disconnectDevice,
  resetDevice,
} = deviceSlice.actions;

export default deviceSlice.reducer; 