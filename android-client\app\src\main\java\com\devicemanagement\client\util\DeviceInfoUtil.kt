package com.devicemanagement.client.util

import android.app.ActivityManager
import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.os.Build
import android.os.Environment
import android.os.StatFs
import android.provider.Settings
import android.telephony.TelephonyManager
import java.io.File
import java.util.Locale
import java.util.UUID

/**
 * 设备信息工具类，用于获取设备的各种信息
 */
object DeviceInfoUtil {

    /**
     * 生成唯一的设备ID
     */
    fun generateDeviceId(context: Context): String {
        val androidId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        return if (androidId != null && androidId != "9774d56d682e549c" && androidId.length > 8) {
            // 使用Android ID
            androidId
        } else {
            // 使用随机UUID，由于无法保证Android ID的唯一性，尤其是在某些ROM上
            UUID.randomUUID().toString()
        }
    }

    /**
     * 获取设备型号
     */
    fun getDeviceModel(): String {
        return Build.MANUFACTURER + " " + Build.MODEL
    }

    /**
     * 获取Android版本
     */
    fun getAndroidVersion(): String {
        return "Android " + Build.VERSION.RELEASE + " (API " + Build.VERSION.SDK_INT + ")"
    }

    /**
     * 获取电池电量百分比
     */
    fun getBatteryPercentage(context: Context): Int {
        val intent = context.registerReceiver(null, android.content.IntentFilter(android.content.Intent.ACTION_BATTERY_CHANGED))
        val level = intent?.getIntExtra(android.os.BatteryManager.EXTRA_LEVEL, -1) ?: -1
        val scale = intent?.getIntExtra(android.os.BatteryManager.EXTRA_SCALE, -1) ?: -1
        
        return if (level != -1 && scale != -1) {
            (level * 100 / scale.toFloat()).toInt()
        } else {
            -1
        }
    }

    /**
     * 获取内部存储总空间（GB）
     */
    fun getTotalInternalStorage(): Float {
        val path = Environment.getDataDirectory()
        val stat = StatFs(path.path)
        val blockSize = stat.blockSizeLong
        val totalBlocks = stat.blockCountLong
        return (totalBlocks * blockSize) / (1024f * 1024f * 1024f)
    }

    /**
     * 获取内部存储可用空间（GB）
     */
    fun getAvailableInternalStorage(): Float {
        val path = Environment.getDataDirectory()
        val stat = StatFs(path.path)
        val blockSize = stat.blockSizeLong
        val availableBlocks = stat.availableBlocksLong
        return (availableBlocks * blockSize) / (1024f * 1024f * 1024f)
    }

    /**
     * 获取已用内部存储空间（GB）
     */
    fun getUsedInternalStorage(): Float {
        return getTotalInternalStorage() - getAvailableInternalStorage()
    }

    /**
     * 获取已安装应用列表
     */
    fun getInstalledApps(context: Context, includeSystemApps: Boolean = false): List<ApplicationInfo> {
        val packageManager = context.packageManager
        val apps = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
        
        return if (includeSystemApps) {
            apps
        } else {
            apps.filter { app ->
                app.flags and ApplicationInfo.FLAG_SYSTEM == 0
            }
        }
    }

    /**
     * 获取可用内存（RAM）百分比
     */
    fun getAvailableMemoryPercentage(context: Context): Int {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        return ((memoryInfo.availMem.toFloat() / memoryInfo.totalMem.toFloat()) * 100).toInt()
    }

    /**
     * 获取设备语言
     */
    fun getDeviceLanguage(): String {
        return Locale.getDefault().language
    }

    /**
     * 获取设备当前网络连接类型
     */
    fun getNetworkType(context: Context): String {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as android.net.ConnectivityManager
        val activeNetwork = connectivityManager.activeNetworkInfo
        
        return when {
            activeNetwork == null -> "未连接"
            activeNetwork.type == android.net.ConnectivityManager.TYPE_WIFI -> "WiFi"
            activeNetwork.type == android.net.ConnectivityManager.TYPE_MOBILE -> {
                when ((activeNetwork as android.net.NetworkInfo).subtype) {
                    TelephonyManager.NETWORK_TYPE_GPRS,
                    TelephonyManager.NETWORK_TYPE_EDGE,
                    TelephonyManager.NETWORK_TYPE_CDMA,
                    TelephonyManager.NETWORK_TYPE_1xRTT,
                    TelephonyManager.NETWORK_TYPE_IDEN -> "2G"
                    
                    TelephonyManager.NETWORK_TYPE_UMTS,
                    TelephonyManager.NETWORK_TYPE_EVDO_0,
                    TelephonyManager.NETWORK_TYPE_EVDO_A,
                    TelephonyManager.NETWORK_TYPE_HSDPA,
                    TelephonyManager.NETWORK_TYPE_HSUPA,
                    TelephonyManager.NETWORK_TYPE_HSPA,
                    TelephonyManager.NETWORK_TYPE_EVDO_B,
                    TelephonyManager.NETWORK_TYPE_EHRPD,
                    TelephonyManager.NETWORK_TYPE_HSPAP -> "3G"
                    
                    TelephonyManager.NETWORK_TYPE_LTE -> "4G"
                    
                    TelephonyManager.NETWORK_TYPE_NR -> "5G"
                    
                    else -> "未知"
                }
            }
            else -> "其他"
        }
    }
} 