package com.devicemanagement.client.repository

import android.content.Context
import com.devicemanagement.client.model.ApiResponse
import com.devicemanagement.client.model.DeviceInfo
import com.devicemanagement.client.util.EncryptionUtil
import com.devicemanagement.client.util.NetworkUtil
import com.devicemanagement.client.util.PreferenceManager
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import timber.log.Timber
import java.io.File
import java.util.concurrent.atomic.AtomicReference

/**
 * 设备数据仓库，处理与服务器的数据交互
 */
class DeviceRepository private constructor(private val context: Context) {

    private val gson = Gson()
    private val baseUrl = "https://device-management-server.example.com/api" // 替换为实际的服务器URL

    /**
     * 注册设备，获取设备令牌
     */
    suspend fun registerDevice(deviceInfo: DeviceInfo): ApiResponse<String> = withContext(Dispatchers.IO) {
        try {
            // 获取本地RSA公钥
            val publicKeyBase64 = PreferenceManager.getString(context, PreferenceManager.KEY_RSA_PUBLIC, "")
            val jsonBody = JSONObject().apply {
                put("device_id", deviceInfo.deviceId)
                put("device_model", deviceInfo.deviceModel)
                put("android_version", deviceInfo.androidVersion)
                put("manufacturer", deviceInfo.manufacturer)
                put("public_key", publicKeyBase64)
            }
            
            val response = NetworkUtil.postJson(
                "$baseUrl/devices/register",
                jsonBody
            )
            
            if (response.isSuccess()) {
                val jsonResponse = JSONObject(response.getOrNull() ?: return@withContext ApiResponse.Error("空响应"))
                
                if (jsonResponse.has("token")) {
                    val token = jsonResponse.getString("token")
                    return@withContext ApiResponse.Success(token)
                } else {
                    return@withContext ApiResponse.Error("响应缺少令牌")
                }
            } else {
                return@withContext response as ApiResponse<String>
            }
        } catch (e: Exception) {
            Timber.e(e, "注册设备失败")
            return@withContext ApiResponse.Error(e.message ?: "未知错误")
        }
    }

    /**
     * 同步设备信息到服务器
     */
    suspend fun syncDeviceInfo(deviceInfo: DeviceInfo, token: String): ApiResponse<String> = withContext(Dispatchers.IO) {
        try {
            val jsonBody = JSONObject(gson.toJson(deviceInfo))
            
            val headers = mapOf(
                "Authorization" to "Bearer $token",
                "Content-Type" to "application/json"
            )
            
            val response = NetworkUtil.postJson(
                "$baseUrl/devices/sync",
                jsonBody,
                headers
            )
            
            return@withContext response
        } catch (e: Exception) {
            Timber.e(e, "同步设备信息失败")
            return@withContext ApiResponse.Error(e.message ?: "未知错误")
        }
    }

    /**
     * 检查是否有待执行的命令
     */
    suspend fun checkPendingCommands(deviceId: String, token: String): ApiResponse<List<JSONObject>> = withContext(Dispatchers.IO) {
        try {
            val headers = mapOf(
                "Authorization" to "Bearer $token"
            )
            
            val response = NetworkUtil.get(
                "$baseUrl/devices/$deviceId/commands",
                headers
            )
            
            if (response.isSuccess()) {
                val jsonResponse = response.getOrNull()
                if (jsonResponse.isNullOrEmpty()) {
                    return@withContext ApiResponse.Success(emptyList())
                }
                
                val jsonArray = JSONArray(jsonResponse)
                val commands = mutableListOf<JSONObject>()
                
                for (i in 0 until jsonArray.length()) {
                    val command = jsonArray.getJSONObject(i)
                    commands.add(command)
                }
                
                return@withContext ApiResponse.Success(commands)
            } else {
                return@withContext ApiResponse.Error(
                    (response as ApiResponse.Error).message,
                    response.code
                )
            }
        } catch (e: Exception) {
            Timber.e(e, "检查待执行命令失败")
            return@withContext ApiResponse.Error(e.message ?: "未知错误")
        }
    }

    /**
     * 上报命令执行结果
     */
    suspend fun reportCommandResult(
        commandId: String,
        isSuccess: Boolean,
        message: String
    ): ApiResponse<String> = withContext(Dispatchers.IO) {
        try {
            val jsonBody = JSONObject().apply {
                put("command_id", commandId)
                put("success", isSuccess)
                put("message", message)
                put("timestamp", System.currentTimeMillis())
            }
            
            val deviceId = getDeviceId()
            val token = getDeviceToken()
            
            if (deviceId.isNullOrEmpty() || token.isNullOrEmpty()) {
                return@withContext ApiResponse.Error("设备未注册")
            }
            
            val headers = mapOf(
                "Authorization" to "Bearer $token",
                "Content-Type" to "application/json"
            )
            
            val response = NetworkUtil.postJson(
                "$baseUrl/devices/$deviceId/command-results",
                jsonBody,
                headers
            )
            
            return@withContext response
        } catch (e: Exception) {
            Timber.e(e, "上报命令执行结果失败")
            return@withContext ApiResponse.Error(e.message ?: "未知错误")
        }
    }

    /**
     * 上传屏幕截图
     */
    suspend fun uploadScreenshot(deviceId: String, token: String, filePath: String): ApiResponse<String> = withContext(Dispatchers.IO) {
        try {
            val headers = mapOf(
                "Authorization" to "Bearer $token"
            )
            
            return@withContext NetworkUtil.uploadFile(
                "$baseUrl/devices/$deviceId/screenshots",
                filePath,
                "screenshot",
                "image/jpeg",
                mapOf("timestamp" to System.currentTimeMillis().toString()),
                headers
            )
        } catch (e: Exception) {
            Timber.e(e, "上传屏幕截图失败")
            return@withContext ApiResponse.Error(e.message ?: "未知错误")
        }
    }

    /**
     * 上传相机图像
     */
    suspend fun uploadCameraImage(deviceId: String, token: String, filePath: String): ApiResponse<String> = withContext(Dispatchers.IO) {
        try {
            val headers = mapOf(
                "Authorization" to "Bearer $token"
            )
            
            return@withContext NetworkUtil.uploadFile(
                "$baseUrl/devices/$deviceId/camera-images",
                filePath,
                "image",
                "image/jpeg",
                mapOf("timestamp" to System.currentTimeMillis().toString()),
                headers
            )
        } catch (e: Exception) {
            Timber.e(e, "上传相机图像失败")
            return@withContext ApiResponse.Error(e.message ?: "未知错误")
        }
    }

    /**
     * 上传地理位置
     */
    suspend fun uploadLocation(deviceId: String, token: String, latitude: Double, longitude: Double, timestamp: Long): ApiResponse<String> = withContext(Dispatchers.IO) {
        try {
            val headers = mapOf(
                "Authorization" to "Bearer $token",
                "Content-Type" to "application/json"
            )
            val jsonBody = org.json.JSONObject().apply {
                put("latitude", latitude)
                put("longitude", longitude)
                put("timestamp", timestamp)
            }
            return@withContext NetworkUtil.postJson(
                "$baseUrl/devices/$deviceId/locations",
                jsonBody,
                headers
            )
        } catch (e: Exception) {
            Timber.e(e, "上传地理位置失败")
            return@withContext ApiResponse.Error(e.message ?: "未知错误")
        }
    }

    /**
     * 上传音频文件
     */
    suspend fun uploadAudio(deviceId: String, token: String, filePath: String): ApiResponse<String> = withContext(Dispatchers.IO) {
        try {
            val headers = mapOf(
                "Authorization" to "Bearer $token"
            )
            return@withContext NetworkUtil.uploadFile(
                "$baseUrl/devices/$deviceId/audio-recordings",
                filePath,
                "audio",
                "audio/mp4",
                mapOf("timestamp" to System.currentTimeMillis().toString()),
                headers
            )
        } catch (e: Exception) {
            Timber.e(e, "上传音频文件失败")
            return@withContext ApiResponse.Error(e.message ?: "未知错误")
        }
    }

    /**
     * 从本地获取设备ID
     */
    private fun getDeviceId(): String? {
        return com.devicemanagement.client.util.PreferenceManager.getString(
            context,
            com.devicemanagement.client.util.PreferenceManager.KEY_DEVICE_ID,
            null
        )
    }

    /**
     * 从本地获取设备令牌
     */
    private fun getDeviceToken(): String? {
        return com.devicemanagement.client.util.PreferenceManager.getString(
            context,
            com.devicemanagement.client.util.PreferenceManager.KEY_DEVICE_TOKEN,
            null
        )
    }

    companion object {
        private val INSTANCE = AtomicReference<DeviceRepository>()
        
        /**
         * 获取设备数据仓库实例
         */
        fun getInstance(context: Context): DeviceRepository {
            return INSTANCE.get() ?: synchronized(this) {
                val instance = DeviceRepository(context.applicationContext)
                INSTANCE.set(instance)
                instance
            }
        }
    }
} 