package com.devicemanagement.client.util

import android.content.Context
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 安全日志工具类，支持本地写入和读取安全相关操作日志
 */
object SecurityLogUtil {
    private const val PREF_KEY = "security_log"
    private const val MAX_LOG_COUNT = 200

    /**
     * 写入安全日志
     */
    fun log(context: Context, message: String) {
        val time = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
        val entry = "$time $message"
        val logs = getLogs(context).toMutableList()
        logs.add(entry)
        // 保持最大日志条数
        while (logs.size > MAX_LOG_COUNT) logs.removeAt(0)
        PreferenceManager.setString(context, PREF_KEY, logs.joinToString("\n"))
    }

    /**
     * 读取全部安全日志
     */
    fun getLogs(context: Context): List<String> {
        val logStr = PreferenceManager.getString(context, PREF_KEY, "")
        return if (logStr.isEmpty()) emptyList() else logStr.split("\n")
    }

    /**
     * 清空安全日志
     */
    fun clearLogs(context: Context) {
        PreferenceManager.setString(context, PREF_KEY, "")
    }
} 