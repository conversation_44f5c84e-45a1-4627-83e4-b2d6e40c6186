import { configureStore } from '@reduxjs/toolkit';
import deviceReducer from './slices/deviceSlice';
import monitoringReducer from './slices/monitoringSlice';
import uiReducer from './slices/uiSlice';

export const store = configureStore({
  reducer: {
    device: deviceReducer,
    monitoring: monitoringReducer,
    ui: uiReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch; 