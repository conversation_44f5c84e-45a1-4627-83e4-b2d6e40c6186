package com.devicemanagement.client.util

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager

/**
 * 电池状态工具类
 */
object BatteryUtil {
    /**
     * 获取当前电池电量百分比
     */
    fun getBatteryLevel(context: Context): Int {
        val intent = context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
        val level = intent?.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) ?: -1
        val scale = intent?.getIntExtra(BatteryManager.EXTRA_SCALE, -1) ?: -1
        return if (level != -1 && scale != -1) {
            (level * 100 / scale.toFloat()).toInt()
        } else {
            -1
        }
    }

    /**
     * 判断是否为低电量
     * @param threshold 低电量阈值（百分比）
     */
    fun isLowBattery(context: Context, threshold: Int = 20): <PERSON><PERSON>an {
        val level = getBatteryLevel(context)
        return level in 0..threshold
    }
} 