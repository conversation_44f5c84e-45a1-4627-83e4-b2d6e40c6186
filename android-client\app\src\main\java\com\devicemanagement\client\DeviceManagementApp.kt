package com.devicemanagement.client

import android.app.Application
import android.content.Context
import android.os.PowerManager
import android.provider.Settings
import androidx.work.Configuration
import androidx.work.Constraints
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.NetworkType
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import com.devicemanagement.client.service.ConnectionWorker
import com.devicemanagement.client.util.DeviceInfoUtil
import com.devicemanagement.client.util.EncryptionUtil
import com.devicemanagement.client.util.PreferenceManager
import com.devicemanagement.client.util.SecurityUtil
import com.devicemanagement.client.util.SystemUtil
import java.util.concurrent.TimeUnit

class DeviceManagementApp : Application(), Configuration.Provider {

    companion object {
        private const val CONNECTION_WORKER_TAG = "connection_worker"
        private const val CONNECTION_WORKER_INTERVAL_MINUTES = 15L
        lateinit var instance: DeviceManagementApp
            private set
    }
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        
        // 初始化组件
        initPreferences()
        initDeviceIdentity()
        scheduleBackgroundTasks()
        
        // 检查是否需要自动启动服务
        if (PreferenceManager.getBoolean(this, PreferenceManager.KEY_AUTO_START, false)) {
            SystemUtil.startMonitoringService(this)
        }
    }
    
    override fun getWorkManagerConfiguration(): Configuration {
        return Configuration.Builder()
            .setMinimumLoggingLevel(android.util.Log.INFO)
            .build()
    }
    
    private fun initPreferences() {
        // 初始化偏好设置，如果是首次启动，设置默认值
        val prefs = PreferenceManager.getSharedPreferences(this)
        
        if (!prefs.contains(PreferenceManager.KEY_FIRST_RUN)) {
            with(prefs.edit()) {
                putBoolean(PreferenceManager.KEY_FIRST_RUN, false)
                putString(PreferenceManager.KEY_SERVER_ADDRESS, "https://devicemanagement.example.com")
                putInt(PreferenceManager.KEY_CONNECTION_TIMEOUT, 30)
                putString(PreferenceManager.KEY_CAMERA_QUALITY, "medium")
                putString(PreferenceManager.KEY_AUDIO_QUALITY, "medium")
                putInt(PreferenceManager.KEY_LOCATION_INTERVAL, 300)
                putBoolean(PreferenceManager.KEY_AUTO_START, false)
                apply()
            }
        }
    }
    
    private fun initDeviceIdentity() {
        // 生成或获取设备ID和令牌
        val prefs = PreferenceManager.getSharedPreferences(this)
        
        if (!prefs.contains(PreferenceManager.KEY_DEVICE_ID)) {
            // 生成唯一设备ID
            val deviceId = DeviceInfoUtil.generateDeviceId(this)
            // 生成设备令牌
            val deviceToken = SecurityUtil.generateToken()
            
            with(prefs.edit()) {
                putString(PreferenceManager.KEY_DEVICE_ID, deviceId)
                putString(PreferenceManager.KEY_DEVICE_TOKEN, deviceToken)
                apply()
            }
        }
        // 生成RSA密钥对（如未生成过）
        if (!prefs.contains(PreferenceManager.KEY_RSA_PUBLIC) || !prefs.contains(PreferenceManager.KEY_RSA_PRIVATE)) {
            val keyPair = EncryptionUtil.generateRSAKeyPair()
            val publicKeyBase64 = EncryptionUtil.publicKeyToBase64(keyPair.public)
            val privateKeyBase64 = EncryptionUtil.privateKeyToBase64(keyPair.private)
            with(prefs.edit()) {
                putString(PreferenceManager.KEY_RSA_PUBLIC, publicKeyBase64)
                putString(PreferenceManager.KEY_RSA_PRIVATE, privateKeyBase64)
                apply()
            }
        }
    }
    
    private fun scheduleBackgroundTasks() {
        // 配置后台任务约束
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()
        
        // 创建周期性连接任务
        val connectionWorkRequest = PeriodicWorkRequestBuilder<ConnectionWorker>(
            CONNECTION_WORKER_INTERVAL_MINUTES, TimeUnit.MINUTES
        )
            .setConstraints(constraints)
            .addTag(CONNECTION_WORKER_TAG)
            .build()
        
        // 安排周期性连接任务
        WorkManager.getInstance(this).enqueueUniquePeriodicWork(
            CONNECTION_WORKER_TAG,
            ExistingPeriodicWorkPolicy.REPLACE,
            connectionWorkRequest
        )
    }
    
    fun isIgnoringBatteryOptimizations(): Boolean {
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        val packageName = packageName
        return powerManager.isIgnoringBatteryOptimizations(packageName)
    }
} 