package com.devicemanagement.client.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable

/**
 * 设备信息模型类，用于表示设备的各种信息
 */
data class DeviceInfo(
    /**
     * 设备唯一标识符
     */
    @SerializedName("device_id")
    val deviceId: String,
    
    /**
     * 设备名称/型号
     */
    @SerializedName("device_model")
    val deviceModel: String,
    
    /**
     * Android系统版本
     */
    @SerializedName("android_version")
    val androidVersion: String,
    
    /**
     * 设备制造商
     */
    @SerializedName("manufacturer")
    val manufacturer: String = "",
    
    /**
     * 电池电量
     */
    @SerializedName("battery_level")
    val batteryLevel: Int = 0,
    
    /**
     * 是否充电中
     */
    @SerializedName("is_charging")
    val isCharging: Boolean = false,
    
    /**
     * 总内部存储空间（GB）
     */
    @SerializedName("total_storage")
    val totalStorage: Float = 0f,
    
    /**
     * 可用内部存储空间（GB）
     */
    @SerializedName("available_storage")
    val availableStorage: Float = 0f,
    
    /**
     * 总内存（RAM）空间（GB）
     */
    @SerializedName("total_memory")
    val totalMemory: Float = 0f,
    
    /**
     * 可用内存（RAM）空间（GB）
     */
    @SerializedName("available_memory")
    val availableMemory: Float = 0f,
    
    /**
     * 网络连接类型（WiFi、移动数据等）
     */
    @SerializedName("network_type")
    val networkType: String = "未知",
    
    /**
     * 应用版本
     */
    @SerializedName("app_version")
    val appVersion: String = "",
    
    /**
     * 设备语言
     */
    @SerializedName("device_language")
    val deviceLanguage: String = "",
    
    /**
     * 最后更新时间（时间戳）
     */
    @SerializedName("last_updated")
    val lastUpdated: Long = System.currentTimeMillis(),
    
    /**
     * 设备在线状态
     */
    @SerializedName("is_online")
    val isOnline: Boolean = true,
    
    /**
     * 安装的应用数量
     */
    @SerializedName("installed_apps_count")
    val installedAppsCount: Int = 0,
    
    /**
     * CPU使用率
     */
    @SerializedName("cpu_usage")
    val cpuUsage: Float = 0f,
    
    /**
     * 屏幕分辨率
     */
    @SerializedName("screen_resolution")
    val screenResolution: String = "",
    
    /**
     * 是否Root/越狱
     */
    @SerializedName("is_rooted")
    val isRooted: Boolean = false,
    
    /**
     * IP地址
     */
    @SerializedName("ip_address")
    val ipAddress: String = "",
    
    /**
     * MAC地址
     */
    @SerializedName("mac_address")
    val macAddress: String = "",
    
    /**
     * 设备序列号
     */
    @SerializedName("serial_number")
    val serialNumber: String = "",
    
    /**
     * 设备标签（用于用户自定义标识）
     */
    @SerializedName("device_tag")
    val deviceTag: String = "",
    
    /**
     * 设备分组
     */
    @SerializedName("device_group")
    val deviceGroup: String = "默认"
) : Serializable 