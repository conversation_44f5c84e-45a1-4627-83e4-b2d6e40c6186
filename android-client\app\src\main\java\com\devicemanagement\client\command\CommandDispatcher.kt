package com.devicemanagement.client.command

import android.content.Context
import org.json.JSONObject

/**
 * 命令分发器，根据type分发到对应的命令处理器
 */
object CommandDispatcher {
    private val handlers = mutableMapOf<String, CommandHandler>()

    /**
     * 注册命令处理器
     */
    fun registerHandler(type: String, handler: CommandHandler) {
        handlers[type] = handler
    }

    /**
     * 分发命令
     */
    suspend fun dispatch(context: Context, command: JSONObject) {
        val type = command.optString("type")
        val handler = handlers[type]
        if (handler != null) {
            handler.handle(context, command)
        } else {
            // 未知命令类型，可记录日志或上报
        }
    }
} 