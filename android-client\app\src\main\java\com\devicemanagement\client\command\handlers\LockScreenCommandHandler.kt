package com.devicemanagement.client.command.handlers

import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import android.os.Build
import com.devicemanagement.client.command.CommandHandler
import org.json.JSONObject
import timber.log.Timber
import com.devicemanagement.client.util.SecurityLogUtil

/**
 * 锁屏命令处理器
 */
class LockScreenCommandHandler : CommandHandler {
    override suspend fun handle(context: Context, command: JSONObject) {
        try {
            val dpm = context.getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
            val componentName = ComponentName(context.packageName, "com.devicemanagement.client.DeviceAdminReceiver")
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                dpm.lockNow()
            } else {
                dpm.lockNow()
            }
            Timber.d("已执行锁屏命令")
            SecurityLogUtil.log(context, "执行远程锁屏命令")
        } catch (e: Exception) {
            Timber.e(e, "锁屏命令执行失败")
            SecurityLogUtil.log(context, "锁屏命令执行失败：${e.message}")
        }
    }
} 