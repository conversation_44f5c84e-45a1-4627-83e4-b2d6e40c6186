package com.devicemanagement.client.util

import android.content.Context
import android.content.SharedPreferences

/**
 * 管理应用偏好设置的工具类
 */
object PreferenceManager {
    private const val PREF_NAME = "device_management_prefs"
    
    // 键常量
    const val KEY_FIRST_RUN = "first_run"
    const val KEY_DEVICE_ID = "device_id"
    const val KEY_DEVICE_TOKEN = "device_token"
    const val KEY_SERVER_ADDRESS = "server_address"
    const val KEY_CONNECTION_STATUS = "connection_status"
    const val KEY_CONNECTION_TIMEOUT = "connection_timeout"
    const val KEY_CAMERA_QUALITY = "camera_quality"
    const val KEY_AUDIO_QUALITY = "audio_quality"
    const val KEY_LOCATION_INTERVAL = "location_interval"
    const val KEY_AUTO_START = "auto_start"
    const val KEY_HIDE_ICON = "hide_icon"
    const val KEY_SERVICE_RUNNING = "service_running"
    const val KEY_LAST_CONNECTION_TIME = "last_connection_time"
    const val KEY_RSA_PUBLIC = "rsa_public_key"
    const val KEY_RSA_PRIVATE = "rsa_private_key"
    const val KEY_LOW_BATTERY_THRESHOLD = "low_battery_threshold"
    const val KEY_LOCATION_TRACKING_ENABLED = "location_tracking_enabled"
    const val KEY_LOCATION_UPLOAD_INTERVAL = "location_upload_interval"
    
    /**
     * 获取SharedPreferences实例
     */
    fun getSharedPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 获取字符串值
     */
    fun getString(context: Context, key: String, defaultValue: String = ""): String {
        return getSharedPreferences(context).getString(key, defaultValue) ?: defaultValue
    }
    
    /**
     * 设置字符串值
     */
    fun setString(context: Context, key: String, value: String) {
        getSharedPreferences(context).edit().putString(key, value).apply()
    }
    
    /**
     * 获取整数值
     */
    fun getInt(context: Context, key: String, defaultValue: Int = 0): Int {
        return getSharedPreferences(context).getInt(key, defaultValue)
    }
    
    /**
     * 设置整数值
     */
    fun setInt(context: Context, key: String, value: Int) {
        getSharedPreferences(context).edit().putInt(key, value).apply()
    }
    
    /**
     * 获取布尔值
     */
    fun getBoolean(context: Context, key: String, defaultValue: Boolean = false): Boolean {
        return getSharedPreferences(context).getBoolean(key, defaultValue)
    }
    
    /**
     * 设置布尔值
     */
    fun setBoolean(context: Context, key: String, value: Boolean) {
        getSharedPreferences(context).edit().putBoolean(key, value).apply()
    }
    
    /**
     * 获取长整数值
     */
    fun getLong(context: Context, key: String, defaultValue: Long = 0L): Long {
        return getSharedPreferences(context).getLong(key, defaultValue)
    }
    
    /**
     * 设置长整数值
     */
    fun setLong(context: Context, key: String, value: Long) {
        getSharedPreferences(context).edit().putLong(key, value).apply()
    }
    
    /**
     * 删除键
     */
    fun remove(context: Context, key: String) {
        getSharedPreferences(context).edit().remove(key).apply()
    }
    
    /**
     * 清除所有偏好设置
     */
    fun clear(context: Context) {
        getSharedPreferences(context).edit().clear().apply()
    }
} 