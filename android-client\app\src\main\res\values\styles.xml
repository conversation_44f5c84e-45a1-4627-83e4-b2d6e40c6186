<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 基础应用主题 -->
    <style name="Theme.DeviceManagementClient" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- 主要颜色 -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="colorAccent">@color/accent</item>
        <item name="android:colorBackground">@color/background</item>
        
        <!-- 文本颜色 -->
        <item name="android:textColorPrimary">@color/primary_text</item>
        <item name="android:textColorSecondary">@color/secondary_text</item>
        
        <!-- 状态栏和导航栏 -->
        <item name="android:statusBarColor">@color/primary_dark</item>
        <item name="android:navigationBarColor">@color/primary_dark</item>
        
        <!-- 禁用活动转换动画 -->
        <item name="android:windowAnimationStyle">@null</item>
    </style>
    
    <!-- 主要按钮样式 -->
    <style name="Widget.Button.Primary" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:minHeight">48dp</item>
    </style>
    
    <!-- 次要按钮样式 -->
    <style name="Widget.Button.Secondary" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textColor">@color/primary</item>
        <item name="strokeColor">@color/primary</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:minHeight">48dp</item>
    </style>
    
    <!-- 文本按钮样式 -->
    <style name="Widget.Button.Text" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/primary</item>
        <item name="android:paddingStart">8dp</item>
        <item name="android:paddingEnd">8dp</item>
        <item name="android:minHeight">36dp</item>
    </style>
    
    <!-- 卡片样式 -->
    <style name="Widget.CardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">2dp</item>
        <item name="contentPadding">16dp</item>
        <item name="android:layout_marginStart">8dp</item>
        <item name="android:layout_marginEnd">8dp</item>
        <item name="android:layout_marginTop">4dp</item>
        <item name="android:layout_marginBottom">4dp</item>
    </style>
    
    <!-- 标题文本样式 -->
    <style name="TextAppearance.Title" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textColor">@color/primary_text</item>
        <item name="android:textSize">18sp</item>
    </style>
    
    <!-- 副标题文本样式 -->
    <style name="TextAppearance.Subtitle" parent="TextAppearance.MaterialComponents.Subtitle1">
        <item name="android:textColor">@color/secondary_text</item>
        <item name="android:textSize">14sp</item>
    </style>
    
    <!-- 正文文本样式 -->
    <style name="TextAppearance.Body" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textColor">@color/primary_text</item>
        <item name="android:textSize">14sp</item>
    </style>
    
    <!-- 开关样式 -->
    <style name="Widget.Switch" parent="Widget.MaterialComponents.CompoundButton.Switch">
        <item name="thumbTint">@color/primary</item>
        <item name="trackTint">@color/primary_light</item>
    </style>
</resources> 