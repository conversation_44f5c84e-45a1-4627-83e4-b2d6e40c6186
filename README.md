# 设备管理系统 - Android客户端

## 项目概述

设备管理系统Android客户端是一个用于远程管理和监控Android设备的应用程序。通过与服务器端配合使用，可以实现设备信息收集、状态监控、远程控制等功能。

## 主要功能

1. **设备信息收集**：自动收集设备型号、Android版本、电池电量、存储空间等信息
2. **后台监控服务**：支持在后台运行监控服务，定期拍照和截屏
3. **远程命令执行**：接收并执行服务器下发的各种命令
4. **定时数据同步**：定期将收集到的信息同步到服务器
5. **安全通信**：与服务器之间的所有通信均采用加密方式进行

## 技术架构

- **编程语言**：Kotlin
- **网络通信**：使用HttpURLConnection进行RESTful API调用
- **后台服务**：使用WorkManager和前台Service保证后台任务执行
- **数据存储**：使用SharedPreferences存储配置和设备标识
- **相机操作**：使用Camera2 API实现拍照功能

## 核心组件说明

### 1. 应用程序类 (DeviceManagementApp)

应用程序的入口点，负责初始化各种组件，包括：
- 初始化设备标识
- 配置后台任务
- 根据设置启动监控服务

```kotlin
// 获取应用实例
val app = DeviceManagementApp.getInstance()
```

### 2. 偏好设置管理器 (PreferenceManager)

用于管理应用程序的各种设置，提供了一系列便捷方法：

```kotlin
// 获取设备ID
val deviceId = PreferenceManager.getString(context, PreferenceManager.KEY_DEVICE_ID, "")

// 设置服务运行状态
PreferenceManager.setBoolean(context, PreferenceManager.KEY_SERVICE_RUNNING, true)
```

### 3. 设备信息工具类 (DeviceInfoUtil)

提供了获取设备各种信息的方法：

```kotlin
// 获取设备型号
val model = DeviceInfoUtil.getDeviceModel()

// 获取电池电量
val batteryLevel = DeviceInfoUtil.getBatteryPercentage(context)

// 获取存储空间信息
val totalStorage = DeviceInfoUtil.getTotalInternalStorage()
val availableStorage = DeviceInfoUtil.getAvailableInternalStorage()
```

### 4. 网络工具类 (NetworkUtil)

处理与服务器的网络通信：

```kotlin
// 检查网络是否可用
val isNetworkAvailable = NetworkUtil.isNetworkAvailable(context)

// 发送GET请求
val response = NetworkUtil.get(url, headers)

// 发送POST请求（JSON）
val response = NetworkUtil.postJson(url, jsonBody, headers)

// 上传文件
val response = NetworkUtil.uploadFile(url, filePath, fieldName, mimeType, params, headers)
```

### 5. 设备数据仓库 (DeviceRepository)

管理与服务器的数据交互：

```kotlin
// 获取实例
val repository = DeviceRepository.getInstance(context)

// 注册设备
val response = repository.registerDevice(deviceInfo)

// 同步设备信息
val response = repository.syncDeviceInfo(deviceInfo, token)

// 检查待执行命令
val commandsResponse = repository.checkPendingCommands(deviceId, token)
```

### 6. 连接工作器 (ConnectionWorker)

使用WorkManager定期执行的后台任务，负责：
- 同步设备信息
- 检查服务器命令
- 管理监控服务的运行状态

### 7. 监控服务 (MonitoringService)

前台服务，负责：
- 定期截屏
- 定期拍照
- 将收集到的图像上传到服务器

## 配置项

应用程序包含以下主要配置项：

| 配置项 | 说明 | 默认值 |
|-------|------|-------|
| KEY_FIRST_RUN | 是否首次运行 | true |
| KEY_DEVICE_ID | 设备唯一标识 | 自动生成 |
| KEY_DEVICE_TOKEN | 设备令牌 | 注册后获取 |
| KEY_SERVICE_RUNNING | 监控服务是否运行 | false |
| KEY_SCREEN_MONITOR_ENABLED | 是否启用屏幕监控 | false |
| KEY_CAMERA_MONITOR_ENABLED | 是否启用相机监控 | false |
| KEY_CAMERA_QUALITY | 相机图像质量 | 1 (中) |
| KEY_SYNC_INTERVAL | 同步间隔（分钟） | 15 |

## 服务端API

客户端与服务器通信使用以下API：

| 端点 | 方法 | 说明 |
|-----|-----|-----|
| /api/devices/register | POST | 设备注册 |
| /api/devices/sync | POST | 同步设备信息 |
| /api/devices/{id}/commands | GET | 获取待执行命令 |
| /api/devices/{id}/command-results | POST | 上报命令执行结果 |
| /api/devices/{id}/screenshots | POST | 上传屏幕截图 |
| /api/devices/{id}/camera-images | POST | 上传相机图像 |

## 端到端加密说明

本客户端已支持端到端加密，保护数据在传输过程中的安全：

1. **密钥生成**：
   - 每台设备首次启动时自动生成一对RSA密钥对（公钥和私钥），并安全存储在本地。
2. **注册时上传公钥**：
   - 设备注册时会将公钥上传到服务器，服务器可用该公钥加密下发的敏感数据。
3. **数据加密流程**：
   - 客户端与服务器通信时，敏感数据会用AES加密，AES密钥再用RSA公钥加密。
   - 服务器返回的数据也可用设备公钥加密，客户端用本地私钥解密。
4. **本地加密工具**：
   - 见`EncryptionUtil.kt`，支持AES和RSA加解密。

**注意：** 端到端加密为数据安全提供了双重保障，即使数据被截获也无法被破解。

## 远程命令扩展机制说明

本客户端支持灵活的远程命令扩展，便于后续添加新命令类型：

1. **命令格式**：
   - 服务器下发命令采用统一JSON结构，如：
     ```json
     { "command_id": "xxx", "type": "LOCK_SCREEN", "params": { "duration": 60 } }
     ```
2. **命令分发器**：
   - `CommandDispatcher`根据type字段自动分发到对应的命令处理器。
3. **命令处理器**：
   - 每种命令类型实现`CommandHandler`接口，单独处理自己的业务逻辑。
   - 示例：`LockScreenCommandHandler`实现了锁屏命令。
4. **扩展方法**：
   - 新增命令时，只需实现新的处理器并在`ConnectionWorker`中注册即可。

**示例：**
```kotlin
// 注册命令处理器
CommandDispatcher.registerHandler("LOCK_SCREEN", LockScreenCommandHandler())
```

这样可以让远程命令的扩展和维护变得非常简单。

## 电池优化说明

本客户端已针对电池使用做了多项优化：

1. **低电量自适应**：
   - 可在设置中配置低电量阈值（默认20%），当电池电量低于该值时，后台同步和监控任务会自动暂停或降低频率，节省电量。
2. **智能休眠**：
   - 监控服务在无任务或低电量时自动休眠，定时唤醒检查。
3. **兼容系统省电模式**：
   - 支持Android Doze模式，建议将应用加入电池优化白名单以保证后台任务正常。

**开发者建议**：如需自定义低电量阈值，可通过`PreferenceManager.KEY_LOW_BATTERY_THRESHOLD`进行设置。

## 地理位置跟踪说明

本客户端支持定时采集和上传设备地理位置：

1. **权限要求**：
   - 需授予`ACCESS_FINE_LOCATION`和`ACCESS_COARSE_LOCATION`权限，Android 10+建议加`ACCESS_BACKGROUND_LOCATION`。
2. **配置项**：
   - `PreferenceManager.KEY_LOCATION_TRACKING_ENABLED`：是否启用位置跟踪（默认false）。
   - `PreferenceManager.KEY_LOCATION_UPLOAD_INTERVAL`：上传间隔（秒，默认300）。
3. **低电量/无权限自适应**：
   - 低电量或无权限时自动暂停位置采集。
4. **服务器接口**：
   - 上传到`/api/devices/{id}/locations`，包含经纬度和时间戳。

**开发者建议**：如需自定义采集频率或开关，可通过PreferenceManager相关配置项设置。

## 远程文件访问说明

本客户端支持远程浏览设备指定目录下的文件和文件夹：

1. **命令格式**：
   - 服务器下发命令：
     ```json
     { "type": "LIST_FILES", "params": { "dir": "/storage/emulated/0/Download" } }
     ```
2. **白名单目录**：
   - 仅允许访问`/storage/emulated/0/Download`、`Documents`、`Pictures`、`Music`、`Movies`等安全目录。
3. **安全控制**：
   - 操作前自动校验路径合法性，防止越权访问。
4. **扩展方法**：
   - 可通过命令扩展机制实现文件上传、下载、删除等操作。

**开发者建议**：如需开放更多目录，可在`FileUtil.allowedRoots`中配置。

## 远程音频录制说明

本客户端支持通过远程命令控制设备录音并自动上传音频文件：

1. **命令格式**：
   - 服务器下发命令：
     ```json
     { "type": "RECORD_AUDIO", "params": { "duration": 10, "quality": 1 } }
     ```
   - `duration`为录音时长（秒），`quality`为音质（0=低，1=中，2=高）。
2. **权限要求**：
   - 需授予`RECORD_AUDIO`和存储权限。
3. **隐私保护**：
   - 录音前自动校验权限，未授权时自动跳过。
   - 录音和上传过程有详细日志记录。
4. **上传接口**：
   - 录音完成后自动上传到`/api/devices/{id}/audio-recordings`。

**开发者建议**：如需自定义录音参数，可通过命令params字段设置。

## 自定义监控规则说明

本客户端支持通过规则引擎灵活配置监控任务的触发条件和执行内容：

1. **规则格式**：
   - 规则采用JSON结构描述，如：
     ```json
     { "id": "rule1", "type": "TIME", "params": { "hour": 8, "minute": 0 }, "action": "CAPTURE_SCREEN" }
     { "id": "rule2", "type": "PERIOD", "params": { "interval": 600 }, "action": "CAPTURE_CAMERA" }
     ```
2. **支持类型**：
   - `TIME`：定时触发（如每天8:00执行）
   - `PERIOD`：周期触发（如每隔10分钟执行）
   - 未来可扩展`EVENT`（事件）、`GEOFENCE`（地理围栏）等
3. **支持动作**：
   - `CAPTURE_SCREEN`：截屏
   - `CAPTURE_CAMERA`：拍照
   - 可扩展更多动作（如录音、上传文件等）
4. **配置方法**：
   - 规则可由服务器下发或本地通过`MonitorRuleManager`管理。

**开发者建议**：如需扩展规则类型或动作，只需在`MonitoringService`和`MonitorRuleManager`中增加对应逻辑。

## 未来改进计划

1. 添加端到端加密功能
2. 支持远程命令扩展
3. 优化电池使用
4. 增加地理位置跟踪
5. 实现远程文件访问
6. 添加远程音频录制
7. 支持自定义监控规则
8. 增强安全性和隐私保护

## 开发者文档

更多详细的开发者文档，请参考项目代码中的类注释和方法文档。 