package com.devicemanagement.client.util

import android.content.Context
import android.media.MediaRecorder
import android.os.Environment
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 录音工具类，封装MediaRecorder录音逻辑
 */
object AudioRecorderUtil {
    private var mediaRecorder: MediaRecorder? = null
    private var outputFile: String? = null

    /**
     * 开始录音
     * @param context 上下文
     * @param durationSec 录音时长（秒）
     * @param quality 音质（0=低，1=中，2=高）
     * @return 录音文件路径
     */
    suspend fun startRecording(context: Context, durationSec: Int = 10, quality: Int = 1): String? = withContext(Dispatchers.IO) {
        try {
            val dir = context.getExternalFilesDir(Environment.DIRECTORY_MUSIC) ?: context.filesDir
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "audio_$timestamp.m4a"
            val file = File(dir, fileName)
            outputFile = file.absolutePath

            mediaRecorder = MediaRecorder().apply {
                setAudioSource(MediaRecorder.AudioSource.MIC)
                setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
                setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
                setOutputFile(outputFile)
                when (quality) {
                    0 -> setAudioEncodingBitRate(64000)
                    2 -> setAudioEncodingBitRate(192000)
                    else -> setAudioEncodingBitRate(128000)
                }
                prepare()
                start()
            }
            // 等待指定时长
            kotlinx.coroutines.delay(durationSec * 1000L)
            stopRecording()
            return@withContext outputFile
        } catch (e: Exception) {
            stopRecording()
            return@withContext null
        }
    }

    /**
     * 停止录音
     */
    fun stopRecording() {
        try {
            mediaRecorder?.apply {
                stop()
                reset()
                release()
            }
        } catch (_: Exception) {}
        mediaRecorder = null
        outputFile = null
    }
} 