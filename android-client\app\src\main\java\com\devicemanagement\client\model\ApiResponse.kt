package com.devicemanagement.client.model

/**
 * API响应封装类，用于处理网络请求的成功和失败情况
 */
sealed class ApiResponse<out T> {
    
    /**
     * 成功响应，包含响应数据
     */
    data class Success<T>(val data: T) : ApiResponse<T>()
    
    /**
     * 错误响应，包含错误信息和错误码
     */
    data class Error(val message: String, val code: Int = -1) : ApiResponse<Nothing>()
    
    /**
     * 加载中状态
     */
    object Loading : ApiResponse<Nothing>()
    
    /**
     * 根据条件执行相应的处理
     * @param onSuccess 成功时的处理
     * @param onError 错误时的处理
     * @param onLoading 加载中的处理
     */
    inline fun <R> fold(
        crossinline onSuccess: (T) -> R,
        crossinline onError: (message: String, code: Int) -> R,
        crossinline onLoading: () -> R = { throw IllegalStateException("不应该在Loading状态下调用") }
    ): R {
        return when (this) {
            is Success -> onSuccess(data)
            is Error -> onError(message, code)
            is Loading -> onLoading()
        }
    }
    
    /**
     * 将成功的响应转换为另一种类型
     */
    inline fun <R> map(crossinline transform: (T) -> R): ApiResponse<R> {
        return when (this) {
            is Success -> Success(transform(data))
            is Error -> this
            is Loading -> this
        }
    }
    
    /**
     * 获取成功响应中的数据，如果响应不是成功的，则返回null
     */
    fun getOrNull(): T? {
        return (this as? Success)?.data
    }
    
    /**
     * 获取成功响应中的数据，如果响应不是成功的，则返回默认值
     */
    fun getOrDefault(defaultValue: @UnsafeVariance T): T {
        return (this as? Success)?.data ?: defaultValue
    }
    
    /**
     * 获取成功响应中的数据，如果响应不是成功的，则抛出异常
     */
    fun getOrThrow(): T {
        return when (this) {
            is Success -> data
            is Error -> throw RuntimeException(message)
            is Loading -> throw IllegalStateException("请求仍在加载中")
        }
    }
    
    /**
     * 判断响应是否成功
     */
    fun isSuccess(): Boolean = this is Success
    
    /**
     * 判断响应是否为错误
     */
    fun isError(): Boolean = this is Error
    
    /**
     * 判断响应是否正在加载
     */
    fun isLoading(): Boolean = this is Loading
} 