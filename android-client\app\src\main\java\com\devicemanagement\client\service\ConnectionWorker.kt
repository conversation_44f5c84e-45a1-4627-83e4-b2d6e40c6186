package com.devicemanagement.client.service

import android.content.Context
import android.content.Intent
import androidx.core.content.ContextCompat
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.devicemanagement.client.DeviceManagementApp
import com.devicemanagement.client.model.DeviceInfo
import com.devicemanagement.client.repository.DeviceRepository
import com.devicemanagement.client.util.DeviceInfoUtil
import com.devicemanagement.client.util.PreferenceManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import timber.log.Timber
import com.devicemanagement.client.command.CommandDispatcher
import com.devicemanagement.client.command.handlers.LockScreenCommandHandler
import com.devicemanagement.client.command.handlers.ListFilesCommandHandler
import com.devicemanagement.client.util.BatteryUtil
import com.devicemanagement.client.command.handlers.RecordAudioCommandHandler

/**
 * 后台连接工作器，用于维持与服务器的连接和定时同步设备信息
 */
class ConnectionWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {

    private val deviceRepository by lazy {
        DeviceRepository.getInstance(applicationContext)
    }

    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            Timber.d("连接工作器启动")
            
            // 注册命令处理器（可扩展）
            CommandDispatcher.registerHandler("LOCK_SCREEN", LockScreenCommandHandler())
            CommandDispatcher.registerHandler("LIST_FILES", ListFilesCommandHandler())
            CommandDispatcher.registerHandler("RECORD_AUDIO", RecordAudioCommandHandler())
            
            // 检查低电量，低电量时跳过同步或减少频率
            val lowBatteryThreshold = PreferenceManager.getInt(applicationContext, PreferenceManager.KEY_LOW_BATTERY_THRESHOLD, 20)
            if (BatteryUtil.isLowBattery(applicationContext, lowBatteryThreshold)) {
                Timber.w("电池电量过低，跳过本次同步以节省电量")
                return@withContext Result.success()
            }
            
            // 检查设备是否已注册
            val deviceId = PreferenceManager.getString(
                applicationContext,
                PreferenceManager.KEY_DEVICE_ID,
                ""
            )
            
            val deviceToken = PreferenceManager.getString(
                applicationContext,
                PreferenceManager.KEY_DEVICE_TOKEN,
                ""
            )
            
            if (deviceId.isNullOrEmpty() || deviceToken.isNullOrEmpty()) {
                Timber.e("设备未注册，无法连接服务器")
                return@withContext Result.failure()
            }
            
            // 收集最新的设备信息
            val deviceInfo = collectDeviceInfo(deviceId)
            
            // 同步设备信息到服务器
            val syncResult = deviceRepository.syncDeviceInfo(deviceInfo, deviceToken)
            
            if (syncResult.isSuccess()) {
                Timber.d("设备信息同步成功")
                
                // 更新连接状态
                PreferenceManager.setBoolean(
                    applicationContext,
                    PreferenceManager.KEY_IS_CONNECTED,
                    true
                )
                
                // 检查是否有待执行的命令
                checkPendingCommands(deviceId, deviceToken)
                
                // 检查服务是否应该运行
                checkMonitoringService()
                
                return@withContext Result.success()
            } else {
                Timber.e("设备信息同步失败: ${syncResult.getOrNull()}")
                
                // 更新连接状态
                PreferenceManager.setBoolean(
                    applicationContext,
                    PreferenceManager.KEY_IS_CONNECTED,
                    false
                )
                
                return@withContext Result.retry()
            }
        } catch (e: Exception) {
            Timber.e(e, "连接工作器异常")
            return@withContext Result.retry()
        }
    }

    /**
     * 收集设备信息
     */
    private fun collectDeviceInfo(deviceId: String): DeviceInfo {
        val context = applicationContext
        
        return DeviceInfo(
            deviceId = deviceId,
            deviceModel = DeviceInfoUtil.getDeviceModel(),
            androidVersion = DeviceInfoUtil.getAndroidVersion(),
            manufacturer = android.os.Build.MANUFACTURER,
            batteryLevel = DeviceInfoUtil.getBatteryPercentage(context),
            totalStorage = DeviceInfoUtil.getTotalInternalStorage(),
            availableStorage = DeviceInfoUtil.getAvailableInternalStorage(),
            networkType = DeviceInfoUtil.getNetworkType(context),
            deviceLanguage = DeviceInfoUtil.getDeviceLanguage(),
            installedAppsCount = DeviceInfoUtil.getInstalledApps(context).size,
            // 其他信息可根据需要添加
            lastUpdated = System.currentTimeMillis()
        )
    }

    /**
     * 检查是否有待执行的命令
     */
    private suspend fun checkPendingCommands(deviceId: String, deviceToken: String) {
        try {
            val commandsResponse = deviceRepository.checkPendingCommands(deviceId, deviceToken)
            
            if (commandsResponse.isSuccess()) {
                val commands = commandsResponse.getOrNull() ?: return
                
                if (commands.isNotEmpty()) {
                    for (command in commands) {
                        executeCommand(command)
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "检查待执行命令异常")
        }
    }

    /**
     * 执行服务器下发的命令（使用命令分发器）
     */
    private suspend fun executeCommand(command: JSONObject) {
        try {
            CommandDispatcher.dispatch(applicationContext, command)
        } catch (e: Exception) {
            Timber.e(e, "执行命令异常")
        }
    }

    /**
     * 检查监控服务是否应该运行
     */
    private fun checkMonitoringService() {
        val shouldRun = PreferenceManager.getBoolean(
            applicationContext,
            PreferenceManager.KEY_SERVICE_RUNNING,
            false
        )
        
        val isRunning = MonitoringService.isRunning
        
        if (shouldRun && !isRunning) {
            // 启动服务
            val intent = Intent(applicationContext, MonitoringService::class.java)
            ContextCompat.startForegroundService(applicationContext, intent)
            Timber.d("监控服务已启动")
        } else if (!shouldRun && isRunning) {
            // 停止服务
            val intent = Intent(applicationContext, MonitoringService::class.java)
            applicationContext.stopService(intent)
            Timber.d("监控服务已停止")
        }
    }

    companion object {
        const val WORK_NAME = "connection_worker"
    }
} 