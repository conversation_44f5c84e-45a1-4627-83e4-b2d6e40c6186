package com.devicemanagement.client.util

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkInfo
import android.os.Build
import com.devicemanagement.client.model.ApiResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import java.net.URLEncoder

/**
 * 网络工具类，用于处理网络连接和请求
 */
object NetworkUtil {

    private const val CONNECTION_TIMEOUT = 15000
    private const val READ_TIMEOUT = 15000
    private const val DEFAULT_CHARSET = "UTF-8"

    /**
     * 检查网络是否可用
     */
    fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities != null && (
                    capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                    capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
                    capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET))
        } else {
            val activeNetworkInfo = connectivityManager.activeNetworkInfo
            activeNetworkInfo != null && activeNetworkInfo.isConnected
        }
    }

    /**
     * 获取当前网络类型
     */
    fun getNetworkType(context: Context): String {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            when {
                capabilities == null -> "无网络"
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "WiFi"
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> "移动数据"
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> "以太网"
                else -> "其他"
            }
        } else {
            val activeNetworkInfo = connectivityManager.activeNetworkInfo
            when {
                activeNetworkInfo == null -> "无网络"
                activeNetworkInfo.type == ConnectivityManager.TYPE_WIFI -> "WiFi"
                activeNetworkInfo.type == ConnectivityManager.TYPE_MOBILE -> "移动数据"
                activeNetworkInfo.type == ConnectivityManager.TYPE_ETHERNET -> "以太网"
                else -> "其他"
            }
        }
    }

    /**
     * 异步GET请求
     */
    suspend fun get(url: String, headers: Map<String, String> = mapOf()): ApiResponse<String> = withContext(Dispatchers.IO) {
        var connection: HttpURLConnection? = null
        
        try {
            val requestUrl = URL(url)
            connection = requestUrl.openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.connectTimeout = CONNECTION_TIMEOUT
            connection.readTimeout = READ_TIMEOUT
            
            // 添加请求头
            headers.forEach { (key, value) ->
                connection.setRequestProperty(key, value)
            }
            
            val responseCode = connection.responseCode
            
            return@withContext if (responseCode == HttpURLConnection.HTTP_OK) {
                val response = connection.inputStream.bufferedReader().use { it.readText() }
                ApiResponse.Success(response)
            } else {
                val errorMessage = connection.errorStream?.bufferedReader()?.use { it.readText() } ?: "未知错误"
                ApiResponse.Error(errorMessage, responseCode)
            }
        } catch (e: Exception) {
            return@withContext ApiResponse.Error(e.message ?: "未知网络错误")
        } finally {
            connection?.disconnect()
        }
    }

    /**
     * 异步POST请求 (JSON)
     */
    suspend fun postJson(url: String, jsonBody: JSONObject, headers: Map<String, String> = mapOf()): ApiResponse<String> = withContext(Dispatchers.IO) {
        var connection: HttpURLConnection? = null
        
        try {
            val requestUrl = URL(url)
            connection = requestUrl.openConnection() as HttpURLConnection
            connection.requestMethod = "POST"
            connection.connectTimeout = CONNECTION_TIMEOUT
            connection.readTimeout = READ_TIMEOUT
            connection.doOutput = true
            connection.setRequestProperty("Content-Type", "application/json; charset=$DEFAULT_CHARSET")
            
            // 添加请求头
            headers.forEach { (key, value) ->
                connection.setRequestProperty(key, value)
            }
            
            // 写入请求体
            OutputStreamWriter(connection.outputStream, DEFAULT_CHARSET).use { writer ->
                writer.write(jsonBody.toString())
                writer.flush()
            }
            
            val responseCode = connection.responseCode
            
            return@withContext if (responseCode == HttpURLConnection.HTTP_OK) {
                val response = connection.inputStream.bufferedReader().use { it.readText() }
                ApiResponse.Success(response)
            } else {
                val errorMessage = connection.errorStream?.bufferedReader()?.use { it.readText() } ?: "未知错误"
                ApiResponse.Error(errorMessage, responseCode)
            }
        } catch (e: Exception) {
            return@withContext ApiResponse.Error(e.message ?: "未知网络错误")
        } finally {
            connection?.disconnect()
        }
    }

    /**
     * 异步POST请求 (表单)
     */
    suspend fun postForm(url: String, formData: Map<String, String>, headers: Map<String, String> = mapOf()): ApiResponse<String> = withContext(Dispatchers.IO) {
        var connection: HttpURLConnection? = null
        
        try {
            val requestUrl = URL(url)
            connection = requestUrl.openConnection() as HttpURLConnection
            connection.requestMethod = "POST"
            connection.connectTimeout = CONNECTION_TIMEOUT
            connection.readTimeout = READ_TIMEOUT
            connection.doOutput = true
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded; charset=$DEFAULT_CHARSET")
            
            // 添加请求头
            headers.forEach { (key, value) ->
                connection.setRequestProperty(key, value)
            }
            
            // 构建表单数据
            val postData = StringBuilder()
            formData.forEach { (key, value) ->
                if (postData.isNotEmpty()) postData.append("&")
                postData.append(URLEncoder.encode(key, DEFAULT_CHARSET))
                postData.append("=")
                postData.append(URLEncoder.encode(value, DEFAULT_CHARSET))
            }
            
            // 写入请求体
            OutputStreamWriter(connection.outputStream, DEFAULT_CHARSET).use { writer ->
                writer.write(postData.toString())
                writer.flush()
            }
            
            val responseCode = connection.responseCode
            
            return@withContext if (responseCode == HttpURLConnection.HTTP_OK) {
                val response = connection.inputStream.bufferedReader().use { it.readText() }
                ApiResponse.Success(response)
            } else {
                val errorMessage = connection.errorStream?.bufferedReader()?.use { it.readText() } ?: "未知错误"
                ApiResponse.Error(errorMessage, responseCode)
            }
        } catch (e: Exception) {
            return@withContext ApiResponse.Error(e.message ?: "未知网络错误")
        } finally {
            connection?.disconnect()
        }
    }

    /**
     * 异步文件上传
     * @param url 服务器URL
     * @param filePath 文件路径
     * @param fieldName 表单字段名
     * @param mimeType 文件MIME类型
     * @param params 附加参数
     * @param headers 请求头
     */
    suspend fun uploadFile(url: String, filePath: String, fieldName: String, mimeType: String, 
                          params: Map<String, String> = mapOf(), headers: Map<String, String> = mapOf()): ApiResponse<String> = withContext(Dispatchers.IO) {
        var connection: HttpURLConnection? = null
        
        try {
            val file = java.io.File(filePath)
            if (!file.exists()) {
                return@withContext ApiResponse.Error("文件不存在")
            }
            
            val boundary = "===" + System.currentTimeMillis() + "==="
            val lineEnd = "\r\n"
            val twoHyphens = "--"
            
            val requestUrl = URL(url)
            connection = requestUrl.openConnection() as HttpURLConnection
            connection.requestMethod = "POST"
            connection.connectTimeout = CONNECTION_TIMEOUT
            connection.readTimeout = READ_TIMEOUT
            connection.doOutput = true
            connection.doInput = true
            connection.useCaches = false
            connection.setRequestProperty("Connection", "Keep-Alive")
            connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=$boundary")
            
            // 添加请求头
            headers.forEach { (key, value) ->
                connection.setRequestProperty(key, value)
            }
            
            val outputStream = connection.outputStream
            val writer = OutputStreamWriter(outputStream)
            
            // 添加参数
            params.forEach { (key, value) ->
                writer.append(twoHyphens + boundary + lineEnd)
                writer.append("Content-Disposition: form-data; name=\"$key\"$lineEnd")
                writer.append(lineEnd)
                writer.append(value)
                writer.append(lineEnd)
                writer.flush()
            }
            
            // 添加文件
            writer.append(twoHyphens + boundary + lineEnd)
            writer.append("Content-Disposition: form-data; name=\"$fieldName\"; filename=\"${file.name}\"$lineEnd")
            writer.append("Content-Type: $mimeType$lineEnd")
            writer.append(lineEnd)
            writer.flush()
            
            // 写入文件数据
            val fileInputStream = java.io.FileInputStream(file)
            val buffer = ByteArray(4096)
            var bytesRead: Int
            while (fileInputStream.read(buffer).also { bytesRead = it } != -1) {
                outputStream.write(buffer, 0, bytesRead)
            }
            outputStream.flush()
            fileInputStream.close()
            
            // 添加结束标记
            writer.append(lineEnd)
            writer.append(twoHyphens + boundary + twoHyphens + lineEnd)
            writer.flush()
            writer.close()
            
            val responseCode = connection.responseCode
            
            return@withContext if (responseCode == HttpURLConnection.HTTP_OK) {
                val response = connection.inputStream.bufferedReader().use { it.readText() }
                ApiResponse.Success(response)
            } else {
                val errorMessage = connection.errorStream?.bufferedReader()?.use { it.readText() } ?: "未知错误"
                ApiResponse.Error(errorMessage, responseCode)
            }
        } catch (e: Exception) {
            return@withContext ApiResponse.Error(e.message ?: "未知网络错误")
        } finally {
            connection?.disconnect()
        }
    }

    /**
     * 异步下载文件
     */
    suspend fun downloadFile(url: String, destPath: String, headers: Map<String, String> = mapOf()): ApiResponse<String> = withContext(Dispatchers.IO) {
        var connection: HttpURLConnection? = null
        
        try {
            val requestUrl = URL(url)
            connection = requestUrl.openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.connectTimeout = CONNECTION_TIMEOUT
            connection.readTimeout = READ_TIMEOUT
            
            // 添加请求头
            headers.forEach { (key, value) ->
                connection.setRequestProperty(key, value)
            }
            
            val responseCode = connection.responseCode
            
            if (responseCode != HttpURLConnection.HTTP_OK) {
                val errorMessage = connection.errorStream?.bufferedReader()?.use { it.readText() } ?: "未知错误"
                return@withContext ApiResponse.Error(errorMessage, responseCode)
            }
            
            val destFile = java.io.File(destPath)
            
            // 确保目标目录存在
            val parentDir = destFile.parentFile
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs()
            }
            
            // 写入文件
            val fileOutputStream = java.io.FileOutputStream(destFile)
            val inputStream = connection.inputStream
            val buffer = ByteArray(4096)
            var bytesRead: Int
            
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                fileOutputStream.write(buffer, 0, bytesRead)
            }
            
            fileOutputStream.close()
            inputStream.close()
            
            return@withContext ApiResponse.Success(destFile.absolutePath)
        } catch (e: Exception) {
            return@withContext ApiResponse.Error(e.message ?: "未知网络错误")
        } finally {
            connection?.disconnect()
        }
    }
} 