package com.devicemanagement.client.util

import android.content.Context
import com.devicemanagement.client.model.MonitorRule
import org.json.JSONArray
import org.json.JSONObject

/**
 * 监控规则管理器，负责规则的存储、加载和管理
 */
object MonitorRuleManager {
    private const val PREF_KEY = "monitor_rules_json"

    /**
     * 加载所有规则
     */
    fun loadRules(context: Context): List<MonitorRule> {
        val json = PreferenceManager.getString(context, PREF_KEY, "[]")
        val arr = JSONArray(json)
        val rules = mutableListOf<MonitorRule>()
        for (i in 0 until arr.length()) {
            val obj = arr.getJSONObject(i)
            rules.add(
                MonitorRule(
                    id = obj.optString("id"),
                    type = obj.optString("type"),
                    params = obj.optJSONObject("params") ?: JSONObject(),
                    action = obj.optString("action")
                )
            )
        }
        return rules
    }

    /**
     * 保存所有规则
     */
    fun saveRules(context: Context, rules: List<MonitorRule>) {
        val arr = JSONArray()
        rules.forEach { rule ->
            arr.put(
                JSONObject().apply {
                    put("id", rule.id)
                    put("type", rule.type)
                    put("params", rule.params)
                    put("action", rule.action)
                }
            )
        }
        PreferenceManager.setString(context, PREF_KEY, arr.toString())
    }

    /**
     * 添加规则
     */
    fun addRule(context: Context, rule: MonitorRule) {
        val rules = loadRules(context).toMutableList()
        rules.removeAll { it.id == rule.id }
        rules.add(rule)
        saveRules(context, rules)
    }

    /**
     * 删除规则
     */
    fun deleteRule(context: Context, ruleId: String) {
        val rules = loadRules(context).filter { it.id != ruleId }
        saveRules(context, rules)
    }

    /**
     * 查询规则
     */
    fun getRule(context: Context, ruleId: String): MonitorRule? {
        return loadRules(context).find { it.id == ruleId }
    }
} 