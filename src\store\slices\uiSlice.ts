import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface UiState {
  activeTab: string;
  sidebar: {
    isOpen: boolean;
  };
  theme: 'light' | 'dark';
  notifications: {
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    message: string;
    timestamp: string;
    read: boolean;
  }[];
  confirmDialog: {
    isOpen: boolean;
    title: string;
    message: string;
    confirmAction: string | null;
  };
}

const initialState: UiState = {
  activeTab: 'dashboard',
  sidebar: {
    isOpen: true,
  },
  theme: 'light',
  notifications: [],
  confirmDialog: {
    isOpen: false,
    title: '',
    message: '',
    confirmAction: null,
  },
};

export const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setActiveTab: (state, action: PayloadAction<string>) => {
      state.activeTab = action.payload;
    },
    toggleSidebar: (state) => {
      state.sidebar.isOpen = !state.sidebar.isOpen;
    },
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebar.isOpen = action.payload;
    },
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
    },
    setTheme: (state, action: PayloadAction<UiState['theme']>) => {
      state.theme = action.payload;
    },
    addNotification: (
      state,
      action: PayloadAction<{
        type: UiState['notifications'][0]['type'];
        message: string;
      }>
    ) => {
      state.notifications.push({
        id: Date.now().toString(),
        type: action.payload.type,
        message: action.payload.message,
        timestamp: new Date().toISOString(),
        read: false,
      });
    },
    markNotificationAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(
        (n) => n.id === action.payload
      );
      if (notification) {
        notification.read = true;
      }
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
    openConfirmDialog: (
      state,
      action: PayloadAction<{
        title: string;
        message: string;
        confirmAction: string | null;
      }>
    ) => {
      state.confirmDialog = {
        isOpen: true,
        title: action.payload.title,
        message: action.payload.message,
        confirmAction: action.payload.confirmAction,
      };
    },
    closeConfirmDialog: (state) => {
      state.confirmDialog.isOpen = false;
    },
  },
});

export const {
  setActiveTab,
  toggleSidebar,
  setSidebarOpen,
  toggleTheme,
  setTheme,
  addNotification,
  markNotificationAsRead,
  clearNotifications,
  openConfirmDialog,
  closeConfirmDialog,
} = uiSlice.actions;

export default uiSlice.reducer; 