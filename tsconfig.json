{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@pages/*": ["src/pages/*"], "@store/*": ["src/store/*"], "@api/*": ["src/api/*"], "@hooks/*": ["src/hooks/*"], "@utils/*": ["src/utils/*"], "@assets/*": ["src/assets/*"], "@styles/*": ["src/styles/*"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}