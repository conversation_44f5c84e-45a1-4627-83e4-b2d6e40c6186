package com.devicemanagement.client.util

import java.io.File

/**
 * 文件工具类，支持安全地列出指定目录下的文件和文件夹
 */
object FileUtil {
    // 允许远程访问的根目录白名单（可根据实际需求调整）
    private val allowedRoots = listOf(
        "/storage/emulated/0/Download",
        "/storage/emulated/0/Documents",
        "/storage/emulated/0/Pictures",
        "/storage/emulated/0/Music",
        "/storage/emulated/0/Movies"
    )

    /**
     * 列出指定目录下的文件和文件夹（只允许白名单目录）
     */
    fun listFilesSafe(dirPath: String): List<File>? {
        if (!isAllowedPath(dirPath)) return null
        val dir = File(dirPath)
        if (!dir.exists() || !dir.isDirectory) return null
        return dir.listFiles()?.toList()
    }

    /**
     * 判断路径是否在白名单内
     */
    fun isAllowedPath(path: String): Boolean {
        return allowedRoots.any { path.startsWith(it) }
    }
} 