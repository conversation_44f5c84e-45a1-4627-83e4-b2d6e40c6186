package com.devicemanagement.client.command.handlers

import android.content.Context
import com.devicemanagement.client.command.CommandHandler
import com.devicemanagement.client.util.FileUtil
import com.devicemanagement.client.util.SecurityLogUtil
import org.json.JSONObject
import timber.log.Timber

/**
 * 文件列表命令处理器
 * 服务器下发命令示例：{"type":"LIST_FILES", "params":{"dir":"/storage/emulated/0/Download"}}
 */
class ListFilesCommandHandler : CommandHandler {
    override suspend fun handle(context: Context, command: JSONObject) {
        val params = command.optJSONObject("params") ?: JSONObject()
        val dir = params.optString("dir", "/storage/emulated/0/Download")
        val files = FileUtil.listFilesSafe(dir)
        if (files != null) {
            val fileList = files.map {
                JSONObject().apply {
                    put("name", it.name)
                    put("isDirectory", it.isDirectory)
                    put("size", it.length())
                    put("lastModified", it.lastModified())
                }
            }
            Timber.d("文件列表：$fileList")
            SecurityLogUtil.log(context, "远程列出目录：$dir，文件数：${fileList.size}")
            // TODO: 可通过DeviceRepository上报文件列表到服务器
        } else {
            Timber.w("目录无效或无权限：$dir")
            SecurityLogUtil.log(context, "远程列目录失败：$dir")
        }
    }
} 