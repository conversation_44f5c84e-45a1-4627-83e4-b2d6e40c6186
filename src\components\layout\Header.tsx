import React from 'react';
import {
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>pography,
  Box,
  Button,
  IconButton,
  Badge,
  Switch,
  Chip,
  TextField,
  InputAdornment,
} from '@mui/material';
import {
  Search as SearchIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
} from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@store/index';
import { toggleTheme } from '@store/slices/uiSlice';

interface HeaderProps {
  deviceConnected: boolean;
}

const Header: React.FC<HeaderProps> = ({ deviceConnected }) => {
  const dispatch = useDispatch();
  const { theme } = useSelector((state: RootState) => state.ui);
  const { systemInfo } = useSelector((state: RootState) => state.device);

  return (
    <AppBar position="static" color="default" elevation={1}>
      <Toolbar>
        <Typography
          variant="h6"
          component="div"
          sx={{ flexGrow: 0, display: { xs: 'none', sm: 'block' }, mr: 2 }}
        >
          移动设备管理
        </Typography>

        <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
          <TextField
            placeholder="搜索设备、文件或应用..."
            size="small"
            sx={{ width: { xs: '100%', sm: 300 } }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }}
          />
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {deviceConnected ? (
            <Chip
              label={systemInfo?.version || 'Android 设备已连接'}
              color="success"
              size="small"
              sx={{ mr: 2 }}
            />
          ) : (
            <Chip
              label="未连接设备"
              color="error"
              size="small"
              sx={{ mr: 2 }}
            />
          )}

          <IconButton size="large" color="inherit">
            <Badge badgeContent={4} color="error">
              <NotificationsIcon />
            </Badge>
          </IconButton>

          <IconButton
            size="large"
            color="inherit"
            onClick={() => dispatch(toggleTheme())}
          >
            {theme === 'dark' ? <LightModeIcon /> : <DarkModeIcon />}
          </IconButton>

          <IconButton size="large" edge="end" color="inherit">
            <SettingsIcon />
          </IconButton>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header; 