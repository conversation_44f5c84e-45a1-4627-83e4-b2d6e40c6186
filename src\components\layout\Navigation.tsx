import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  Box,
  Tabs,
  Tab,
  Divider,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Phone as PhoneIcon,
  Videocam as VideocamIcon,
  Mic as MicIcon,
  Apps as AppsIcon,
  Folder as FolderIcon,
  LocationOn as LocationOnIcon,
  ScreenLockPortrait as ScreenLockIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { RootState } from '@store/index';
import { setActiveTab } from '@store/slices/uiSlice';

interface NavigationTab {
  label: string;
  value: string;
  path: string;
  icon: JSX.Element;
}

const Navigation: React.FC = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { activeTab } = useSelector((state: RootState) => state.ui);
  const { isConnected } = useSelector((state: RootState) => state.device);

  const tabs: NavigationTab[] = [
    {
      label: '仪表盘',
      value: 'dashboard',
      path: '/',
      icon: <DashboardIcon />,
    },
    {
      label: '通讯列表',
      value: 'call-list',
      path: '/call-list',
      icon: <PhoneIcon />,
    },
    {
      label: '摄像监控',
      value: 'camera-monitor',
      path: '/camera-monitor',
      icon: <VideocamIcon />,
    },
    {
      label: '声音监控',
      value: 'audio-monitor',
      path: '/audio-monitor',
      icon: <MicIcon />,
    },
    {
      label: '应用列表',
      value: 'app-list',
      path: '/app-list',
      icon: <AppsIcon />,
    },
    {
      label: '文件管理',
      value: 'file-manager',
      path: '/file-manager',
      icon: <FolderIcon />,
    },
    {
      label: '定位记录',
      value: 'location-tracker',
      path: '/location-tracker',
      icon: <LocationOnIcon />,
    },
    {
      label: '黑屏',
      value: 'screen-lock',
      path: '/screen-lock',
      icon: <ScreenLockIcon />,
    },
    {
      label: '控制',
      value: 'device-control',
      path: '/device-control',
      icon: <SettingsIcon />,
    },
  ];

  const handleChange = (_event: React.SyntheticEvent, newValue: string) => {
    dispatch(setActiveTab(newValue));
  };

  React.useEffect(() => {
    // 根据当前路径设置激活的选项卡
    const currentTab = tabs.find((tab) => tab.path === location.pathname);
    if (currentTab) {
      dispatch(setActiveTab(currentTab.value));
    }
  }, [dispatch, location.pathname, tabs]);

  return (
    <Box
      sx={{
        bgcolor: 'background.paper',
        borderBottom: 1,
        borderColor: 'divider',
        overflow: 'auto',
      }}
    >
      <Tabs
        value={activeTab}
        onChange={handleChange}
        variant={isMobile ? 'scrollable' : 'fullWidth'}
        scrollButtons={isMobile ? 'auto' : false}
        allowScrollButtonsMobile
        aria-label="device monitoring tabs"
        sx={{
          minHeight: '48px',
          '& .MuiTab-root': {
            minHeight: '48px',
            minWidth: isMobile ? '90px' : '120px',
          },
        }}
      >
        {tabs.map((tab) => (
          <Tab
            key={tab.value}
            label={isMobile ? '' : tab.label}
            value={tab.value}
            icon={tab.icon}
            iconPosition={isMobile ? 'top' : 'start'}
            disabled={!isConnected && tab.value !== 'dashboard'}
            component={Link}
            to={tab.path}
            sx={{
              fontSize: '0.75rem',
              fontWeight: 'medium',
            }}
          />
        ))}
      </Tabs>
    </Box>
  );
};

export default Navigation; 