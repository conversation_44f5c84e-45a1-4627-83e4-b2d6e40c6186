* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
  width: 100%;
}

:root {
  --primary-color: #2196F3;
  --secondary-color: #D32F2F;
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --error-color: #F44336;
  --background-color: #F5F5F5;
  --paper-color: #FFFFFF;
  --text-primary: #333333;
  --text-secondary: #757575;
  --text-disabled: #BDBDBD;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

/* 移动设备适配 */
@media screen and (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
} 