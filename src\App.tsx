import { useState } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box } from '@mui/material';

import Header from '@components/layout/Header';
import Navigation from '@components/layout/Navigation';
import Dashboard from '@pages/Dashboard';
import CallList from '@pages/CallList';
import CameraMonitor from '@pages/CameraMonitor';
import AudioMonitor from '@pages/AudioMonitor';
import AppList from '@pages/AppList';
import FileManager from '@pages/FileManager';
import LocationTracker from '@pages/LocationTracker';
import DeviceControl from '@pages/DeviceControl';

const App = () => {
  const [deviceConnected, setDeviceConnected] = useState<boolean>(false);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      <Header deviceConnected={deviceConnected} />
      <Navigation />
      
      <Box component="main" sx={{ flexGrow: 1, overflow: 'auto' }}>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/call-list" element={<CallList />} />
          <Route path="/camera-monitor" element={<CameraMonitor />} />
          <Route path="/audio-monitor" element={<AudioMonitor />} />
          <Route path="/app-list" element={<AppList />} />
          <Route path="/file-manager" element={<FileManager />} />
          <Route path="/location-tracker" element={<LocationTracker />} />
          <Route path="/device-control" element={<DeviceControl />} />
        </Routes>
      </Box>
    </Box>
  );
};

export default App; 