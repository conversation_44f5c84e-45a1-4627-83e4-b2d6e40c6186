import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Typography,
  Button,
  TextField,
  Stack,
  Alert,
  IconButton,
  LinearProgress,
  CircularProgress,
  Divider,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  PhoneAndroid as PhoneIcon,
  Battery90 as BatteryIcon,
  SdStorage as StorageIcon,
  Memory as MemoryIcon,
  Wifi as WifiIcon,
} from '@mui/icons-material';
import { RootState } from '@store/index';
import { setDeviceCredentials, setConnectionStatus, setSystemInfo } from '@store/slices/deviceSlice';

const Dashboard: React.FC = () => {
  const dispatch = useDispatch();
  const { id, token, isConnected, systemInfo } = useSelector(
    (state: RootState) => state.device
  );
  
  const [deviceId, setDeviceId] = useState<string>(id || '');
  const [deviceToken, setDeviceToken] = useState<string>(token || '');
  const [connecting, setConnecting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleConnect = () => {
    if (!deviceId || !deviceToken) {
      setError('请输入设备ID和令牌');
      return;
    }

    setConnecting(true);
    setError(null);

    // 模拟连接设备的过程
    setTimeout(() => {
      dispatch(setDeviceCredentials({ id: deviceId, token: deviceToken }));
      dispatch(setConnectionStatus(true));
      dispatch(
        setSystemInfo({
          version: 'Android 14',
          model: 'Pixel 7 Pro',
          battery: 85,
          storage: {
            total: 128,
            used: 75,
          },
        })
      );
      setConnecting(false);
    }, 1500);
  };

  const handleDisconnect = () => {
    dispatch(setConnectionStatus(false));
  };

  return (
    <Box sx={{ p: 3 }}>
      <Grid container spacing={3}>
        {!isConnected ? (
          <Grid item xs={12} md={6} lg={4}>
            <Card elevation={3}>
              <CardHeader title="连接设备" />
              <CardContent>
                {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
                <Stack spacing={2}>
                  <TextField
                    label="设备 ID"
                    variant="outlined"
                    fullWidth
                    value={deviceId}
                    onChange={(e) => setDeviceId(e.target.value)}
                    disabled={connecting}
                  />
                  <TextField
                    label="设备令牌"
                    variant="outlined"
                    fullWidth
                    value={deviceToken}
                    onChange={(e) => setDeviceToken(e.target.value)}
                    disabled={connecting}
                    type="password"
                  />
                  <Button
                    variant="contained"
                    color="primary"
                    fullWidth
                    onClick={handleConnect}
                    disabled={connecting}
                    startIcon={connecting ? <CircularProgress size={20} /> : null}
                  >
                    {connecting ? '正在连接...' : '连接设备'}
                  </Button>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        ) : (
          <>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h5" component="h1">
                  设备信息
                </Typography>
                <Box>
                  <Button
                    variant="outlined"
                    color="error"
                    onClick={handleDisconnect}
                    sx={{ mr: 1 }}
                  >
                    断开连接
                  </Button>
                  <IconButton color="primary">
                    <RefreshIcon />
                  </IconButton>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card elevation={3}>
                <CardHeader
                  avatar={<PhoneIcon color="primary" />}
                  title="设备型号"
                  subheader={systemInfo?.model || '未知'}
                />
                <CardContent>
                  <Typography variant="body2" color="text.secondary">
                    操作系统: {systemInfo?.version || '未知'}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card elevation={3}>
                <CardHeader
                  avatar={<BatteryIcon color="primary" />}
                  title="电池状态"
                  subheader={`${systemInfo?.battery || 0}%`}
                />
                <CardContent>
                  <LinearProgress
                    variant="determinate"
                    value={systemInfo?.battery || 0}
                    color={
                      (systemInfo?.battery || 0) > 50
                        ? 'success'
                        : (systemInfo?.battery || 0) > 20
                        ? 'warning'
                        : 'error'
                    }
                    sx={{ height: 10, borderRadius: 5 }}
                  />
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card elevation={3}>
                <CardHeader
                  avatar={<StorageIcon color="primary" />}
                  title="存储空间"
                  subheader={`${systemInfo?.storage?.used || 0} GB / ${
                    systemInfo?.storage?.total || 0
                  } GB`}
                />
                <CardContent>
                  <LinearProgress
                    variant="determinate"
                    value={
                      ((systemInfo?.storage?.used || 0) /
                        (systemInfo?.storage?.total || 1)) *
                      100
                    }
                    color={
                      ((systemInfo?.storage?.used || 0) /
                        (systemInfo?.storage?.total || 1)) *
                        100 >
                      80
                        ? 'error'
                        : 'primary'
                    }
                    sx={{ height: 10, borderRadius: 5 }}
                  />
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h5" component="h2" sx={{ mb: 2 }}>
                监控状态
              </Typography>
            </Grid>

            <Grid item xs={6} md={3}>
              <Card elevation={3}>
                <CardContent>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      textAlign: 'center',
                    }}
                  >
                    <WifiIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h6" component="div">
                      屏幕监控
                    </Typography>
                    <Typography
                      variant="body2"
                      color="error"
                      sx={{ mt: 1 }}
                    >
                      未开启
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={6} md={3}>
              <Card elevation={3}>
                <CardContent>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      textAlign: 'center',
                    }}
                  >
                    <MemoryIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h6" component="div">
                      应用监控
                    </Typography>
                    <Typography
                      variant="body2"
                      color="success"
                      sx={{ mt: 1 }}
                    >
                      已开启
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={6} md={3}>
              <Card elevation={3}>
                <CardContent>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      textAlign: 'center',
                    }}
                  >
                    <PhoneIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h6" component="div">
                      通话监控
                    </Typography>
                    <Typography
                      variant="body2"
                      color="success"
                      sx={{ mt: 1 }}
                    >
                      已开启
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={6} md={3}>
              <Card elevation={3}>
                <CardContent>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      textAlign: 'center',
                    }}
                  >
                    <StorageIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h6" component="div">
                      文件监控
                    </Typography>
                    <Typography
                      variant="body2"
                      color="error"
                      sx={{ mt: 1 }}
                    >
                      未开启
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </>
        )}
      </Grid>
    </Box>
  );
};

export default Dashboard; 